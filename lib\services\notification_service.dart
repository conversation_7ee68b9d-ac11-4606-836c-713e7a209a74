import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import '../models/models.dart';
import '../main.dart';
import '../screens/contacts/contact_detail_screen.dart';
import 'data_stream_service.dart';
import 'supabase_service.dart';
import 'offline_contact_service.dart';
import 'connectivity_service.dart';
import 'local_database_service.dart';
import 'emergency_mode_service.dart';

class NotificationService {
  static final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();
  static bool _initialized = false;

  static Future<void> initialize() async {
    if (_initialized) return;

    // Initialize timezone data
    tz.initializeTimeZones();

    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Check for notification that launched the app
    final launchDetails =
        await _notifications.getNotificationAppLaunchDetails();
    if (launchDetails?.didNotificationLaunchApp == true) {
      debugPrint('🔔 App was launched by notification');
      if (launchDetails?.notificationResponse?.payload != null) {
        debugPrint(
            '🔔 Launch notification payload: ${launchDetails!.notificationResponse!.payload}');
        // Handle the launch notification
        _onNotificationTapped(launchDetails.notificationResponse!);
      }
    }

    // Create notification channels for Android with enhanced settings
    const AndroidNotificationChannel mainChannel = AndroidNotificationChannel(
      'contact_times',
      'Contact Times',
      description: 'Notifications for optimal contact times',
      importance: Importance.max,
      playSound: true,
      enableVibration: true,
      enableLights: true,
      ledColor: Color.fromARGB(255, 255, 0, 0),
      showBadge: true,
    );

    const AndroidNotificationChannel reminderChannel =
        AndroidNotificationChannel(
      'contact_reminders',
      'Contact Reminders',
      description: 'Persistent reminders for contact times',
      importance: Importance.high,
      playSound: true,
      enableVibration: true,
      showBadge: true,
    );

    const AndroidNotificationChannel criticalChannel =
        AndroidNotificationChannel(
      'contact_critical',
      'Critical Contact Times',
      description: 'Critical notifications that bypass Do Not Disturb',
      importance: Importance.max,
      playSound: true,
      enableVibration: true,
      enableLights: true,
      ledColor: Color.fromARGB(255, 255, 0, 0),
      showBadge: true,
    );

    final androidPlugin = _notifications.resolvePlatformSpecificImplementation<
        AndroidFlutterLocalNotificationsPlugin>();
    if (androidPlugin != null) {
      await androidPlugin.createNotificationChannel(mainChannel);
      await androidPlugin.createNotificationChannel(reminderChannel);
      await androidPlugin.createNotificationChannel(criticalChannel);
    }

    _initialized = true;

    // Check for any pending navigation from notification taps
    _checkPendingNavigation();
  }

  /// Check and handle any pending navigation from notification taps
  static void _checkPendingNavigation() {
    // Schedule check after a short delay to ensure app is fully initialized
    Future.delayed(const Duration(milliseconds: 500), () {
      if (_pendingNavigation != null) {
        debugPrint(
            '🔔 Checking for pending navigation after app initialization');
        _attemptNavigation();
      }
    });
  }

  /// Public method to trigger pending navigation check (called from app when ready)
  static void checkForPendingNavigation() {
    _checkPendingNavigation();
  }

  static void _onNotificationTapped(NotificationResponse response) {
    // Handle notification tap
    debugPrint('🔔 Notification tapped: ${response.payload}');

    if (response.payload != null) {
      // Parse payload: 'contact_${contactUserId}_slot_${timeSlotId}'
      final payload = response.payload!;
      debugPrint('🔔 Processing payload: $payload');

      if (payload.startsWith('contact_')) {
        final parts = payload.split('_');
        if (parts.length >= 3) {
          final contactUserId = parts[1];
          debugPrint('🔔 Extracted contact user ID: $contactUserId');
          _navigateToContact(contactUserId);
        } else {
          debugPrint('❌ Invalid payload format: $payload');
        }
      } else if (payload.startsWith('test_')) {
        debugPrint('🧪 Test notification tapped: $payload');
        // Handle test notifications - just log for now
      } else {
        debugPrint('❌ Unknown payload format: $payload');
      }
    } else {
      debugPrint('❌ No payload in notification response');
    }
  }

  static void _navigateToContact(String contactUserId) async {
    try {
      debugPrint('🔔 Starting navigation to contact: $contactUserId');

      // Validate input
      if (contactUserId.isEmpty) {
        debugPrint('❌ Empty contact user ID provided');
        return;
      }

      // Get the contact profile using offline-aware service
      final offlineService = OfflineContactService();
      final contactProfile = await offlineService.getProfileById(contactUserId);
      if (contactProfile == null) {
        debugPrint('❌ Contact profile not found: $contactUserId');
        return;
      }

      debugPrint(
          '✅ Contact profile found: ${contactProfile.fullName ?? contactProfile.phoneNumber}');

      // Validate required fields
      if (contactProfile.phoneNumber.isEmpty) {
        debugPrint('❌ Contact profile has no phone number');
        return;
      }

      // Create a simple DeviceContact for the notification tap
      final deviceContact = DeviceContact(
        displayName: contactProfile.fullName ?? contactProfile.phoneNumber,
        phoneNumbers: [contactProfile.phoneNumber],
      );

      // Create a ContactWithProfile object
      final contactWithProfile = ContactWithProfile(
        deviceContact: deviceContact,
        profile: contactProfile,
      );

      // Store the navigation intent for when the app is ready
      _pendingNavigation = contactWithProfile;

      // Try immediate navigation first
      _attemptNavigation();

      // If immediate navigation fails, set up a retry mechanism
      _scheduleNavigationRetry();
    } catch (e) {
      debugPrint('❌ Error navigating to contact: $e');
      // Clear pending navigation on error
      _pendingNavigation = null;
      _navigationRetryCount = 0;
    }
  }

  static ContactWithProfile? _pendingNavigation;
  static int _navigationRetryCount = 0;
  static const int _maxNavigationRetries = 10;

  static void _attemptNavigation() {
    if (_pendingNavigation == null) return;

    final context = navigatorKey.currentContext;
    if (context != null) {
      debugPrint('✅ Navigation context available, navigating to contact');

      // Store the contact locally to avoid null issues in the builder
      final contactToNavigate = _pendingNavigation!;

      // Clear pending navigation before starting navigation
      _pendingNavigation = null;
      _navigationRetryCount = 0;

      // Clear any existing navigation stack and navigate to contact
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(
          builder: (context) => ContactDetailScreen(contact: contactToNavigate),
        ),
        (route) => route.isFirst, // Keep only the first route (dashboard)
      );
    } else {
      debugPrint(
          '⚠️ Navigation context not available yet (retry ${_navigationRetryCount + 1}/$_maxNavigationRetries)');
    }
  }

  static void _scheduleNavigationRetry() {
    if (_pendingNavigation == null ||
        _navigationRetryCount >= _maxNavigationRetries) {
      if (_navigationRetryCount >= _maxNavigationRetries) {
        debugPrint('❌ Max navigation retries reached, giving up');
        _pendingNavigation = null;
        _navigationRetryCount = 0;
      }
      return;
    }

    _navigationRetryCount++;

    // Schedule retry with exponential backoff
    final delay = Duration(milliseconds: 100 * _navigationRetryCount);
    Future.delayed(delay, () {
      try {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          try {
            _attemptNavigation();
            if (_pendingNavigation != null) {
              _scheduleNavigationRetry();
            }
          } catch (e) {
            debugPrint('❌ Error in navigation retry callback: $e');
            _pendingNavigation = null;
            _navigationRetryCount = 0;
          }
        });
      } catch (e) {
        debugPrint('❌ Error scheduling navigation retry: $e');
        _pendingNavigation = null;
        _navigationRetryCount = 0;
      }
    });
  }

  static Future<bool> requestPermissions() async {
    await initialize();

    final androidPlugin = _notifications.resolvePlatformSpecificImplementation<
        AndroidFlutterLocalNotificationsPlugin>();
    final iosPlugin = _notifications.resolvePlatformSpecificImplementation<
        IOSFlutterLocalNotificationsPlugin>();

    bool granted = true;

    if (androidPlugin != null) {
      // Request basic notification permission only
      granted = await androidPlugin.requestNotificationsPermission() ?? false;
      debugPrint('Basic notification permission: $granted');

      // Note: We don't automatically request exact alarms permission
      // Users can enable it manually through settings if they want precise timing
    }

    if (iosPlugin != null) {
      granted = await iosPlugin.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          ) ??
          false;
    }

    return granted;
  }

  static Future<bool> requestExactAlarmsPermission() async {
    await initialize();

    final androidPlugin = _notifications.resolvePlatformSpecificImplementation<
        AndroidFlutterLocalNotificationsPlugin>();

    if (androidPlugin != null) {
      try {
        final exactAlarmPermission =
            await androidPlugin.requestExactAlarmsPermission();
        debugPrint('Exact alarm permission: $exactAlarmPermission');
        return exactAlarmPermission ?? false;
      } catch (e) {
        debugPrint('Exact alarm permission not available or denied: $e');
        return false;
      }
    }

    return true; // iOS doesn't need this permission
  }

  static Future<void> scheduleContactNotifications() async {
    await initialize();

    try {
      // Use offline-aware service
      final offlineService = OfflineContactService();
      final currentProfile = await offlineService.getCurrentProfile();
      if (currentProfile == null) {
        debugPrint('❌ No current profile found for notification scheduling');
        return;
      }

      debugPrint(
          '📱 Scheduling notifications for user: ${currentProfile.fullName}');

      // Get all notification preferences for the current user (offline-aware)
      final allPreferences =
          await _getAllUserNotificationPreferences(currentProfile.id);

      debugPrint('📱 Found ${allPreferences.length} notification preferences');

      // Debug: Print all preferences
      for (int i = 0; i < allPreferences.length; i++) {
        final pref = allPreferences[i];
        debugPrint(
            '📱 Preference $i: enabled=${pref.isEnabled}, timeSlotId=${pref.timeSlotId}, contactUserId=${pref.contactUserId}');
      }

      // Cancel existing notifications
      await _notifications.cancelAll();
      debugPrint('📱 Cancelled existing notifications');

      // Schedule new notifications
      int scheduledCount = 0;
      int enabledCount = 0;
      for (final preference in allPreferences) {
        if (preference.isEnabled) {
          enabledCount++;
          debugPrint(
              '📱 Processing enabled preference ${enabledCount}: ${preference.id}');
          try {
            await _scheduleNotificationForTimeSlot(preference);
            scheduledCount++;
            debugPrint(
                '✅ Successfully scheduled notification for preference ${preference.id}');
          } catch (e) {
            debugPrint(
                '❌ Failed to schedule notification for preference ${preference.id}: $e');
          }
        } else {
          debugPrint('📱 Skipping disabled preference: ${preference.id}');
        }
      }

      debugPrint(
          '✅ Successfully scheduled $scheduledCount out of $enabledCount enabled notifications');

      // Verify notifications were actually scheduled
      try {
        final pendingNotifications =
            await _notifications.pendingNotificationRequests();
        debugPrint(
            '📱 Verification: ${pendingNotifications.length} pending notifications found');
        for (final notification in pendingNotifications) {
          debugPrint(
              '📱   Pending notification: ID=${notification.id}, Title=${notification.title}');
        }
      } catch (e) {
        debugPrint('❌ Error verifying pending notifications: $e');
      }
    } catch (e) {
      debugPrint('❌ Error scheduling notifications: $e');
    }
  }

  static Future<List<NotificationPreference>>
      _getAllUserNotificationPreferences(String userId) async {
    try {
      final connectivityService = ConnectivityService();

      if (connectivityService.isOnline) {
        // When online, try to get from server first
        try {
          final serverPreferences =
              await SupabaseService.getAllUserNotificationPreferences(userId);
          debugPrint(
              '📱 Got ${serverPreferences.length} notification preferences from server');
          return serverPreferences;
        } catch (e) {
          debugPrint(
              '❌ Failed to get preferences from server, falling back to local: $e');
        }
      }

      // Offline mode or server failed - get from local database
      debugPrint('📱 Getting notification preferences from local database...');
      final localPreferences =
          await LocalDatabaseService.getAllUserNotificationPreferences(userId);
      debugPrint(
          '📱 Got ${localPreferences.length} notification preferences from local database');

      return localPreferences;
    } catch (e) {
      debugPrint('❌ Failed to get all user notification preferences: $e');
      return [];
    }
  }

  static Future<void> _scheduleNotificationForTimeSlot(
      NotificationPreference preference) async {
    try {
      // Get the time slot details
      final timeSlot = await _getTimeSlotById(preference.timeSlotId);
      if (timeSlot == null) {
        debugPrint('Time slot not found: ${preference.timeSlotId}');
        return;
      }

      // Get contact details
      final contact = await _getContactById(preference.contactUserId);
      if (contact == null) {
        debugPrint('Contact not found: ${preference.contactUserId}');
        return;
      }

      // Calculate next occurrence of this time slot
      final nextOccurrence = _calculateNextOccurrence(timeSlot);
      if (nextOccurrence == null) {
        debugPrint('Could not calculate next occurrence for time slot');
        return;
      }

      // Schedule notification at the exact time slot starts (not 5 minutes before)
      final notificationTime = nextOccurrence;

      debugPrint('📱 Scheduling notification for ${contact.fullName}:');
      debugPrint('📱   Time slot: ${timeSlot.dayName} ${timeSlot.timeRange}');
      debugPrint('📱   Time slot day of week: ${timeSlot.dayOfWeek}');
      debugPrint('📱   Time slot start time: ${timeSlot.startTime}');
      debugPrint('📱   Time slot end time: ${timeSlot.endTime}');
      debugPrint('📱   Next occurrence: $nextOccurrence');
      debugPrint('📱   Notification time: $notificationTime');
      debugPrint('📱   Current time: ${DateTime.now()}');
      debugPrint(
          '📱   Time until notification: ${notificationTime.difference(DateTime.now())}');

      // Check if user is in emergency mode
      final emergencyService = EmergencyModeService();
      final isEmergencyMode = emergencyService.isEmergencyMode;

      // Prepare notification content based on emergency mode
      String notificationTitle;
      String notificationBody;
      String bigTextContent;

      if (isEmergencyMode) {
        notificationTitle = 'Emergency Mode Active - ${contact.fullName}';
        notificationBody = 'User is in emergency mode and cannot be contacted';
        bigTextContent = emergencyService.getEmergencyMessage();
      } else {
        notificationTitle = 'Good time to contact ${contact.fullName}';
        notificationBody =
            'Their preferred time slot "${timeSlot.dayName}: ${timeSlot.timeRange}" is starting now';
        bigTextContent =
            'Their preferred time slot "${timeSlot.dayName}: ${timeSlot.timeRange}" is starting now. Tap to view contact details.';
      }

      // Only schedule if it's in the future
      if (notificationTime.isAfter(DateTime.now())) {
        final timeUntilNotification =
            notificationTime.difference(DateTime.now());
        debugPrint('📱 Notification is in the future - scheduling');
        debugPrint(
            '📱   Time until notification: ${timeUntilNotification.inMinutes} minutes');
        debugPrint(
            '📱   Time until notification: ${timeUntilNotification.inHours} hours');
        debugPrint(
            '📱   Time until notification: ${timeUntilNotification.inDays} days');

        try {
          final hoursUntil = timeUntilNotification.inHours;
          final isCritical = hoursUntil <= 2; // Critical if within 2 hours

          debugPrint(
              '📱 Notification priority: ${isCritical ? 'CRITICAL' : 'NORMAL'} (${hoursUntil}h until)');

          // Enhanced notification details for better reliability
          final androidDetails = AndroidNotificationDetails(
            isCritical ? 'contact_critical' : 'contact_times',
            isCritical ? 'Critical Contact Times' : 'Contact Times',
            channelDescription: 'Notifications for optimal contact times',
            importance: isCritical ? Importance.max : Importance.high,
            priority: isCritical ? Priority.max : Priority.high,
            icon: '@mipmap/ic_launcher',
            largeIcon:
                const DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
            styleInformation: BigTextStyleInformation(
              bigTextContent,
              htmlFormatBigText: true,
              contentTitle: notificationTitle,
              htmlFormatContentTitle: true,
            ),
            when: notificationTime.millisecondsSinceEpoch,
            usesChronometer: false,
            chronometerCountDown: false,
            showWhen: true,
            enableVibration: true,
            enableLights: true,
            ledColor: const Color.fromARGB(255, 255, 0, 0),
            ledOnMs: 1000,
            ledOffMs: 500,
            ticker: 'Contact time for ${contact.fullName}',
            visibility: NotificationVisibility.public,
            timeoutAfter: 300000, // 5 minutes timeout
            category: AndroidNotificationCategory.reminder,
            fullScreenIntent: isCritical,
            autoCancel: true,
          );

          // Try to schedule exact notification first
          debugPrint('📱 Attempting to schedule exact notification...');
          debugPrint('📱   Notification ID: ${preference.hashCode}');
          debugPrint('📱   Title: $notificationTitle');
          debugPrint('📱   Body: $notificationBody');
          debugPrint(
              '📱   Scheduled time: ${_convertToTZDateTime(notificationTime)}');
          debugPrint(
              '📱   Payload: contact_${preference.contactUserId}_slot_${preference.timeSlotId}');

          await _notifications.zonedSchedule(
            preference.hashCode, // Use preference hash as unique ID
            notificationTitle,
            notificationBody,
            _convertToTZDateTime(notificationTime),
            NotificationDetails(
              android: androidDetails,
              iOS: const DarwinNotificationDetails(
                presentAlert: true,
                presentBadge: true,
                presentSound: true,
                interruptionLevel: InterruptionLevel.timeSensitive,
              ),
            ),
            payload:
                'contact_${preference.contactUserId}_slot_${preference.timeSlotId}',
            uiLocalNotificationDateInterpretation:
                UILocalNotificationDateInterpretation.absoluteTime,
          );

          debugPrint(
              '✅ Exact notification scheduled successfully with ID: ${preference.hashCode}');
        } catch (e) {
          debugPrint(
              'Exact alarms not available, using alternative approach: $e');
          // Use alternative notification strategy
          await _scheduleAlternativeNotification(
            preference.hashCode,
            notificationTitle,
            notificationBody,
            notificationTime,
            'contact_${preference.contactUserId}_slot_${preference.timeSlotId}',
          );
        }
      } else {
        debugPrint('⚠️ Notification time is in the past, not scheduling');
      }
    } catch (e) {
      debugPrint('Error scheduling notification for time slot: $e');
    }
  }

  static Future<TimeSlot?> _getTimeSlotById(String timeSlotId) async {
    try {
      // First try local database
      final timeSlot = await LocalDatabaseService.getTimeSlotById(timeSlotId);
      if (timeSlot != null) {
        debugPrint(
            '✅ Found time slot in local database: ${timeSlot.dayName} ${timeSlot.timeRange}');
        return timeSlot;
      }

      // If not found locally and online, try server
      final connectivityService = ConnectivityService();
      if (connectivityService.isOnline) {
        debugPrint('🔍 Time slot not found locally, trying server...');
        final serverTimeSlot =
            await SupabaseService.getTimeSlotById(timeSlotId);
        if (serverTimeSlot != null) {
          // Cache it locally for future use
          await LocalDatabaseService.insertTimeSlot(serverTimeSlot);
          debugPrint('✅ Found time slot on server and cached locally');
          return serverTimeSlot;
        }
      }

      debugPrint('❌ Time slot not found: $timeSlotId');
      return null;
    } catch (e) {
      debugPrint('❌ Failed to get time slot: $e');
      return null;
    }
  }

  static Future<Profile?> _getContactById(String contactId) async {
    // Use offline-aware service
    final offlineService = OfflineContactService();
    return await offlineService.getProfileById(contactId);
  }

  static DateTime? _calculateNextOccurrence(TimeSlot timeSlot) {
    final now = DateTime.now();

    debugPrint('📱 Calculating next occurrence for time slot:');
    debugPrint('📱   Current time: $now');
    debugPrint(
        '📱   Time slot day: ${timeSlot.dayName} (${timeSlot.dayOfWeek})');
    debugPrint(
        '📱   Time slot time: ${timeSlot.startTime} - ${timeSlot.endTime}');

    // TimeSlot.dayOfWeek should be 0-6 (Sunday=0, Monday=1, etc.)
    // DateTime.weekday is 1-7 (Monday=1, Sunday=7)
    // Convert DateTime.weekday to 0-6 format
    final currentWeekday =
        now.weekday == 7 ? 0 : now.weekday; // Sunday=0, Monday=1, etc.

    debugPrint(
        '📱 Current weekday: $currentWeekday, Time slot weekday: ${timeSlot.dayOfWeek}');

    // Calculate days until next occurrence
    int daysUntilNext = timeSlot.dayOfWeek - currentWeekday;

    // If it's today, check if the time has passed
    if (daysUntilNext == 0) {
      final todaySlotTime = DateTime(
        now.year,
        now.month,
        now.day,
        timeSlot.startTime.hour,
        timeSlot.startTime.minute,
      );

      // If the time slot already started today, schedule for next week
      if (todaySlotTime.isBefore(now.add(const Duration(minutes: 5)))) {
        daysUntilNext = 7; // Next week
      }
    } else if (daysUntilNext < 0) {
      daysUntilNext += 7; // Next week
    }

    final nextDate = now.add(Duration(days: daysUntilNext));
    final nextOccurrence = DateTime(
      nextDate.year,
      nextDate.month,
      nextDate.day,
      timeSlot.startTime.hour,
      timeSlot.startTime.minute,
    );

    debugPrint(
        '📱 Next occurrence calculated: $nextOccurrence (in $daysUntilNext days)');
    debugPrint(
        '📱 Time until next occurrence: ${nextOccurrence.difference(now)}');
    return nextOccurrence;
  }

  static tz.TZDateTime _convertToTZDateTime(DateTime dateTime) {
    final location = tz.local;
    return tz.TZDateTime.from(dateTime, location);
  }

  static Future<void> _scheduleAlternativeNotification(
    int id,
    String title,
    String body,
    DateTime scheduledTime,
    String payload,
  ) async {
    // Alternative notification strategy when exact alarms aren't available
    final now = DateTime.now();
    final timeDifference = scheduledTime.difference(now);

    debugPrint(
        'Alternative notification - Time difference: ${timeDifference.inMinutes} minutes');

    // Strategy 1: For notifications within the next 2 hours, use inexact scheduling
    if (timeDifference.inHours <= 2 && timeDifference.inMinutes > 0) {
      try {
        // Use inexact scheduling with zonedSchedule but without exact timing requirement
        await _notifications.zonedSchedule(
          id,
          title,
          '$body (Approximate timing)',
          _convertToTZDateTime(scheduledTime),
          const NotificationDetails(
            android: AndroidNotificationDetails(
              'contact_times',
              'Contact Times',
              channelDescription: 'Notifications for optimal contact times',
              importance: Importance.high,
              priority: Priority.high,
              icon: '@mipmap/ic_launcher',
            ),
            iOS: DarwinNotificationDetails(
              presentAlert: true,
              presentBadge: true,
              presentSound: true,
            ),
          ),
          payload: payload,
          uiLocalNotificationDateInterpretation:
              UILocalNotificationDateInterpretation.absoluteTime,
          androidScheduleMode: AndroidScheduleMode.inexactAllowWhileIdle,
        );
        debugPrint('✅ Alternative notification scheduled (inexact)');
      } catch (e) {
        debugPrint('Alternative scheduling also failed: $e');
        // Strategy 2: Show a persistent reminder notification
        await _showPersistentReminder(id, title, body, scheduledTime, payload);
      }
    } else {
      debugPrint('Notification too far in future for alternative scheduling');
      // For distant notifications, we'll rely on app reopening to reschedule
    }
  }

  static Future<void> _showPersistentReminder(
    int id,
    String title,
    String body,
    DateTime scheduledTime,
    String payload,
  ) async {
    // Show a persistent notification that reminds user about upcoming contact times
    final timeUntil = scheduledTime.difference(DateTime.now());
    final hours = timeUntil.inHours;
    final minutes = timeUntil.inMinutes % 60;

    await _notifications.show(
      id + 10000, // Different ID for persistent reminders
      'Contact Time Reminder Set',
      'Reminder: $title in ${hours}h ${minutes}m. Check app for exact timing.',
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'contact_reminders',
          'Contact Reminders',
          channelDescription: 'Persistent reminders for contact times',
          importance: Importance.low,
          priority: Priority.low,
          ongoing: true,
          autoCancel: false,
          icon: '@mipmap/ic_launcher',
        ),
        iOS: DarwinNotificationDetails(
          presentAlert: false,
          presentBadge: true,
          presentSound: false,
        ),
      ),
      payload: payload,
    );
    debugPrint('✅ Persistent reminder notification shown');
  }

  static Future<void> cancelNotificationsForContact(
      String contactUserId) async {
    await initialize();

    // Cancel all notifications for this contact
    // This is a simplified implementation
    await _notifications.cancelAll();

    // Reschedule remaining notifications
    await scheduleContactNotifications();
  }

  static Future<bool> canScheduleExactAlarms() async {
    await initialize();

    final androidPlugin = _notifications.resolvePlatformSpecificImplementation<
        AndroidFlutterLocalNotificationsPlugin>();

    if (androidPlugin != null) {
      try {
        return await androidPlugin.canScheduleExactNotifications() ?? false;
      } catch (e) {
        debugPrint('Error checking exact alarm permission: $e');
        return false;
      }
    }

    return true; // iOS doesn't have this restriction
  }

  static Future<bool> requestExactAlarmPermission() async {
    await initialize();

    final androidPlugin = _notifications.resolvePlatformSpecificImplementation<
        AndroidFlutterLocalNotificationsPlugin>();

    if (androidPlugin != null) {
      try {
        return await androidPlugin.requestExactAlarmsPermission() ?? false;
      } catch (e) {
        debugPrint('Error requesting exact alarm permission: $e');
        return false;
      }
    }

    return true; // iOS doesn't need this
  }

  static Future<List<PendingNotificationRequest>>
      getPendingNotifications() async {
    await initialize();
    try {
      return await _notifications.pendingNotificationRequests();
    } catch (e) {
      debugPrint('Error getting pending notifications: $e');
      return [];
    }
  }

  static Future<void> showTestNotification() async {
    await initialize();

    final canScheduleExact = await canScheduleExactAlarms();
    final message = canScheduleExact
        ? 'Notification system is working with exact timing. Tap to test navigation!'
        : 'Notification system is working (exact timing may vary due to system limitations). Tap to test navigation!';

    // Try to get a real contact for testing navigation (offline-aware)
    String testPayload = 'test_quick';
    String contactName = 'Test Contact';
    try {
      final offlineService = OfflineContactService();
      final currentProfile = await offlineService.getCurrentProfile();
      if (currentProfile != null) {
        final allPreferences =
            await _getAllUserNotificationPreferences(currentProfile.id);
        if (allPreferences.isNotEmpty) {
          final firstPreference = allPreferences.first;
          testPayload =
              'contact_${firstPreference.contactUserId}_slot_${firstPreference.timeSlotId}';

          // Get contact name for better message (offline-aware)
          final contactProfile = await offlineService
              .getProfileById(firstPreference.contactUserId);
          if (contactProfile != null) {
            contactName = contactProfile.fullName ?? contactProfile.phoneNumber;
          }
          debugPrint('🧪 Using real contact for test: $contactName');
        }
      }
    } catch (e) {
      debugPrint(
          '🧪 Could not get real contact for test, using default payload: $e');
    }

    await _notifications.show(
      999,
      'Contact Times Test - $contactName',
      message,
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'contact_times',
          'Contact Times',
          channelDescription: 'Notifications for optimal contact times',
          importance: Importance.high,
          priority: Priority.high,
        ),
        iOS: DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        ),
      ),
      payload: testPayload,
    );
  }

  // Simple test for scheduled notifications (for debugging notification issues)
  static Future<void> showTestScheduledNotification(
      {int minutesFromNow = 1}) async {
    await initialize();

    try {
      final scheduledTime =
          DateTime.now().add(Duration(minutes: minutesFromNow));
      debugPrint('📱 Scheduling test notification for: $scheduledTime');
      debugPrint('📱 Current time: ${DateTime.now()}');
      debugPrint(
          '📱 Time difference: ${scheduledTime.difference(DateTime.now()).inMinutes} minutes');

      await _notifications.zonedSchedule(
        1002,
        'Test Scheduled Notification',
        'This test notification was scheduled for $minutesFromNow minute(s) from activation. Current time: ${DateTime.now().toString().substring(11, 19)}',
        _convertToTZDateTime(scheduledTime),
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'contact_times',
            'Contact Times',
            channelDescription: 'Test scheduled notification',
            importance: Importance.max,
            priority: Priority.max,
            icon: '@mipmap/ic_launcher',
            enableVibration: true,
            enableLights: true,
            autoCancel: true,
          ),
          iOS: DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
            interruptionLevel: InterruptionLevel.timeSensitive,
          ),
        ),
        payload: 'test_scheduled',
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
      );

      debugPrint(
          '✅ Test scheduled notification set for $minutesFromNow minute(s)');

      // Verify it was scheduled
      final pending = await _notifications.pendingNotificationRequests();
      debugPrint(
          '📱 Pending notifications after scheduling: ${pending.length}');
      for (final p in pending) {
        if (p.id == 1002) {
          debugPrint(
              '📱 Found our test notification in pending list: ${p.title}');
        }
      }
    } catch (e) {
      debugPrint('❌ Failed to schedule test notification: $e');
    }
  }

  // Debug method to check current notification preferences
  static Future<void> debugNotificationPreferences() async {
    try {
      final offlineService = OfflineContactService();
      final currentProfile = await offlineService.getCurrentProfile();
      if (currentProfile == null) {
        debugPrint('❌ No current profile found');
        return;
      }

      debugPrint('📱 === NOTIFICATION PREFERENCES DEBUG ===');
      debugPrint(
          '📱 Current user: ${currentProfile.fullName} (${currentProfile.id})');

      final allPreferences =
          await _getAllUserNotificationPreferences(currentProfile.id);
      debugPrint(
          '📱 Found ${allPreferences.length} total notification preferences');

      for (int i = 0; i < allPreferences.length; i++) {
        final pref = allPreferences[i];
        debugPrint('📱 Preference ${i + 1}:');
        debugPrint('📱   ID: ${pref.id}');
        debugPrint('📱   Enabled: ${pref.isEnabled}');
        debugPrint('📱   Contact User ID: ${pref.contactUserId}');
        debugPrint('📱   Time Slot ID: ${pref.timeSlotId}');
        debugPrint('📱   Created: ${pref.createdAt}');
        debugPrint('📱   Updated: ${pref.updatedAt}');

        // Try to get contact info
        try {
          final contact = await _getContactById(pref.contactUserId);
          debugPrint('📱   Contact: ${contact?.fullName ?? 'Unknown'}');
        } catch (e) {
          debugPrint('📱   Contact: Error loading ($e)');
        }

        // Try to get time slot info
        try {
          final timeSlot = await _getTimeSlotById(pref.timeSlotId);
          if (timeSlot != null) {
            debugPrint(
                '📱   Time Slot: ${timeSlot.dayName} ${timeSlot.timeRange}');
            final nextOccurrence = _calculateNextOccurrence(timeSlot);
            debugPrint('📱   Next Occurrence: $nextOccurrence');
          } else {
            debugPrint('📱   Time Slot: Not found');
          }
        } catch (e) {
          debugPrint('📱   Time Slot: Error loading ($e)');
        }
        debugPrint('📱   ---');
      }

      // Check pending notifications
      final pending = await _notifications.pendingNotificationRequests();
      debugPrint('📱 Pending system notifications: ${pending.length}');
      for (final p in pending) {
        debugPrint('📱   Pending: ID=${p.id}, Title=${p.title}');
      }

      debugPrint('📱 === END DEBUG ===');
    } catch (e) {
      debugPrint('❌ Error in debug: $e');
    }
  }

  /// Comprehensive test suite for notification reliability across all app states
  static Future<void> runNotificationReliabilityTests() async {
    await initialize();
    debugPrint('🧪 Starting comprehensive notification reliability tests...');

    // Test 1: Immediate notification (foreground test)
    await _testImmediateNotification();

    // Test 2: Short delay notification (background test)
    await _testShortDelayNotification();

    // Test 3: Scheduled notification (app closed test)
    await _testScheduledNotification();

    // Test 4: Critical notification
    await _testCriticalNotification();

    // Test 5: Persistent reminder
    await _testPersistentReminder();

    debugPrint('🧪 All notification reliability tests initiated');
  }

  static Future<void> _testImmediateNotification() async {
    debugPrint('🧪 Test 1: Immediate notification (foreground)');
    await _notifications.show(
      1001,
      'Test 1: Foreground Notification',
      'This tests immediate notification delivery while app is in foreground',
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'contact_times',
          'Contact Times',
          channelDescription: 'Test notification',
          importance: Importance.high,
          priority: Priority.high,
          icon: '@mipmap/ic_launcher',
          enableVibration: true,
          enableLights: true,
        ),
        iOS: DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        ),
      ),
      payload: 'test_foreground',
    );
  }

  static Future<void> _testShortDelayNotification() async {
    debugPrint('🧪 Test 2: Short delay notification (background test)');
    final scheduledTime = DateTime.now().add(const Duration(seconds: 30));

    await _notifications.zonedSchedule(
      1002,
      'Test 2: Background Notification',
      'This tests notification delivery after 30 seconds (put app in background)',
      _convertToTZDateTime(scheduledTime),
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'contact_times',
          'Contact Times',
          channelDescription: 'Test notification',
          importance: Importance.max,
          priority: Priority.max,
          icon: '@mipmap/ic_launcher',
          enableVibration: true,
          enableLights: true,
          fullScreenIntent: true,
        ),
        iOS: DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
          interruptionLevel: InterruptionLevel.timeSensitive,
        ),
      ),
      payload: 'test_background',
      androidScheduleMode: AndroidScheduleMode.inexactAllowWhileIdle,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  static Future<void> _testScheduledNotification() async {
    debugPrint('🧪 Test 3: Scheduled notification (app closed test)');
    final scheduledTime = DateTime.now().add(const Duration(minutes: 2));

    // Get a real contact for testing navigation
    String testPayload = 'test_app_closed';
    try {
      final currentProfile = await SupabaseService.getCurrentProfile();
      if (currentProfile != null) {
        final allPreferences =
            await _getAllUserNotificationPreferences(currentProfile.id);
        if (allPreferences.isNotEmpty) {
          final firstPreference = allPreferences.first;
          testPayload =
              'contact_${firstPreference.contactUserId}_slot_${firstPreference.timeSlotId}';
          debugPrint(
              '🧪 Using real contact for test: ${firstPreference.contactUserId}');
        }
      }
    } catch (e) {
      debugPrint(
          '🧪 Could not get real contact for test, using default payload: $e');
    }

    await _notifications.zonedSchedule(
      1003,
      'Test 3: App Closed Notification',
      'This tests notification delivery after 2 minutes (close the app completely). Tap to test navigation!',
      _convertToTZDateTime(scheduledTime),
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'contact_critical',
          'Critical Contact Times',
          channelDescription: 'Test critical notification',
          importance: Importance.max,
          priority: Priority.max,
          icon: '@mipmap/ic_launcher',
          enableVibration: true,
          enableLights: true,
          fullScreenIntent: true,
          category: AndroidNotificationCategory.alarm,
        ),
        iOS: DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
          interruptionLevel: InterruptionLevel.critical,
        ),
      ),
      payload: testPayload,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  static Future<void> _testCriticalNotification() async {
    debugPrint('🧪 Test 4: Critical notification');
    await _notifications.show(
      1004,
      'Test 4: Critical Notification',
      'This tests critical notification with maximum priority and full screen intent',
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'contact_critical',
          'Critical Contact Times',
          channelDescription: 'Test critical notification',
          importance: Importance.max,
          priority: Priority.max,
          icon: '@mipmap/ic_launcher',
          enableVibration: true,
          enableLights: true,
          fullScreenIntent: true,
          category: AndroidNotificationCategory.alarm,
          visibility: NotificationVisibility.public,
        ),
        iOS: DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
          interruptionLevel: InterruptionLevel.critical,
        ),
      ),
      payload: 'test_critical',
    );
  }

  static Future<void> _testPersistentReminder() async {
    debugPrint('🧪 Test 5: Persistent reminder');
    await _notifications.show(
      1005,
      'Test 5: Persistent Reminder',
      'This tests persistent notification that stays until dismissed',
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'contact_reminders',
          'Contact Reminders',
          channelDescription: 'Test persistent reminder',
          importance: Importance.high,
          priority: Priority.high,
          icon: '@mipmap/ic_launcher',
          ongoing: true,
          autoCancel: false,
        ),
        iOS: DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: false,
        ),
      ),
      payload: 'test_persistent',
    );
  }

  static Future<String> getNotificationStatusMessage() async {
    final canScheduleExact = await canScheduleExactAlarms();

    if (canScheduleExact) {
      return 'Notifications will appear exactly when contact time slots start. Perfect timing guaranteed!';
    } else {
      return 'Using smart alternative timing: approximate notifications for nearby slots, persistent reminders for distant ones. Works well even without exact timing permission!';
    }
  }

  /// Check if the app is whitelisted from battery optimization
  static Future<bool> isBatteryOptimizationDisabled() async {
    final androidPlugin = _notifications.resolvePlatformSpecificImplementation<
        AndroidFlutterLocalNotificationsPlugin>();

    if (androidPlugin != null) {
      try {
        // This is a placeholder - actual implementation would require platform channel
        // For now, we'll assume battery optimization might be enabled
        return false;
      } catch (e) {
        debugPrint('Error checking battery optimization: $e');
        return false;
      }
    }

    return true; // iOS doesn't have this issue
  }

  /// Get comprehensive notification reliability status
  static Future<Map<String, dynamic>> getNotificationReliabilityStatus() async {
    final canScheduleExact = await canScheduleExactAlarms();
    final batteryOptimized = await isBatteryOptimizationDisabled();

    return {
      'canScheduleExact': canScheduleExact,
      'batteryOptimizationDisabled': batteryOptimized,
      'overallReliability':
          _calculateReliabilityScore(canScheduleExact, batteryOptimized),
      'recommendations':
          _getReliabilityRecommendations(canScheduleExact, batteryOptimized),
    };
  }

  static String _calculateReliabilityScore(
      bool exactAlarms, bool batteryOptimized) {
    if (exactAlarms && batteryOptimized) {
      return 'Excellent';
    } else if (exactAlarms || batteryOptimized) {
      return 'Good';
    } else {
      return 'Fair';
    }
  }

  static List<String> _getReliabilityRecommendations(
      bool exactAlarms, bool batteryOptimized) {
    final recommendations = <String>[];

    if (!exactAlarms) {
      recommendations
          .add('Enable "Alarms & reminders" permission for precise timing');
    }

    if (!batteryOptimized) {
      recommendations.add('Disable battery optimization for this app');
      recommendations.add('Add app to "Auto-start" or "Protected apps" list');
    }

    recommendations.add('Keep notifications enabled in system settings');
    recommendations.add('Ensure Do Not Disturb allows this app');

    return recommendations;
  }

  /// Show battery optimization guidance dialog
  static Future<void> showBatteryOptimizationGuidance() async {
    debugPrint('📱 Battery Optimization Guidance:');
    debugPrint('1. Go to Settings → Battery → Battery Optimization');
    debugPrint('2. Find "Contact Times" app');
    debugPrint('3. Select "Don\'t optimize" or "Allow"');
    debugPrint('4. Also check Auto-start/Protected apps settings');
  }
}
