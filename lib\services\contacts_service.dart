import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/user_contact.dart';

class ContactsService {
  static Future<bool> requestContactsPermission() async {
    final status = await Permission.contacts.status;

    if (status.isGranted) {
      return true;
    }

    if (status.isDenied) {
      final result = await Permission.contacts.request();
      return result.isGranted;
    }

    if (status.isPermanentlyDenied) {
      // User has permanently denied permission, need to open app settings
      return false;
    }

    return false;
  }

  static Future<List<DeviceContact>> getDeviceContacts() async {
    final hasPermission = await requestContactsPermission();
    if (!hasPermission) {
      throw Exception('Contacts permission denied');
    }

    try {
      final deviceContacts = await FlutterContacts.getContacts(
        withProperties: true,
      );

      return deviceContacts
          .where((contact) =>
              contact.displayName.isNotEmpty && contact.phones.isNotEmpty)
          .map((contact) => DeviceContact(
                id: contact.id,
                displayName: contact.displayName,
                phoneNumbers: contact.phones
                    .map((phone) => phone.number)
                    .where((phone) => phone.isNotEmpty)
                    .toList(),
                email: contact.emails.isNotEmpty
                    ? contact.emails.first.address
                    : null,
                avatar: null, // Will be handled separately if needed
              ))
          .where((contact) => contact.phoneNumbers.isNotEmpty)
          .toList();
    } catch (e) {
      throw Exception('Failed to load contacts: $e');
    }
  }

  static Future<List<DeviceContact>> getUncategorizedContacts(
      List<UserContact> categorizedContacts) async {
    final allContacts = await getDeviceContacts();
    final categorizedPhones = categorizedContacts
        .map((contact) => contact.categorizedContactPhone)
        .toSet();

    return allContacts
        .where((contact) =>
            !categorizedPhones.contains(contact.cleanedPrimaryPhone))
        .toList();
  }

  static String cleanPhoneNumber(String phone) {
    return phone.replaceAll(RegExp(r'[^\d]'), '');
  }

  /// Generate multiple variations of a phone number for robust matching
  /// This handles cases where contacts are stored with/without country codes
  static List<String> generatePhoneVariations(String phone) {
    final variations = <String>{};

    // Clean the phone number first
    final cleaned = cleanPhoneNumber(phone);
    if (cleaned.isEmpty) return [];

    // Add the cleaned version
    variations.add(cleaned);

    // Add the normalized version (without country code)
    final normalized = normalizePhoneForMatching(phone);
    if (normalized.isNotEmpty) {
      variations.add(normalized);
    }

    // Add with + prefix variations
    variations.add('+$cleaned');
    if (normalized.isNotEmpty) {
      variations.add('+$normalized');
    }

    // For numbers that might have country codes, try common patterns
    // This handles cases where numbers are stored with/without country codes
    if (cleaned.length > 7) {
      // Only for numbers that could have country codes
      // Try removing potential country codes (1-4 digits)
      for (int i = 1; i <= 4 && i < cleaned.length - 6; i++) {
        final withoutPrefix = cleaned.substring(i);
        if (withoutPrefix.length >= 7) {
          // Ensure remaining number is valid
          variations.add(withoutPrefix);
          variations.add('+$withoutPrefix');
        }
      }

      // Try adding potential country codes for common patterns
      // Only if the number doesn't already start with common country code patterns
      if (!_hasCountryCodePattern(cleaned)) {
        final withoutLeadingZero =
            cleaned.startsWith('0') ? cleaned.substring(1) : cleaned;
        variations.add(withoutLeadingZero);
        variations.add('+$withoutLeadingZero');
      }
    }

    return variations.toList();
  }

  /// Check if a phone number already has a country code pattern
  static bool _hasCountryCodePattern(String phone) {
    if (phone.length < 10) return false;

    // Common country code patterns (1-4 digits)
    // This is a simple heuristic - numbers starting with certain patterns
    // are likely to already have country codes
    final commonCountryCodes = [
      '1',
      '7',
      '20',
      '27',
      '30',
      '31',
      '32',
      '33',
      '34',
      '36',
      '39',
      '40',
      '41',
      '43',
      '44',
      '45',
      '46',
      '47',
      '48',
      '49',
      '51',
      '52',
      '53',
      '54',
      '55',
      '56',
      '57',
      '58',
      '60',
      '61',
      '62',
      '63',
      '64',
      '65',
      '66',
      '81',
      '82',
      '84',
      '86',
      '90',
      '91',
      '92',
      '93',
      '94',
      '95',
      '98',
      '212',
      '213',
      '216',
      '218',
      '220',
      '221',
      '222',
      '223',
      '224',
      '225',
      '226',
      '227',
      '228',
      '229',
      '230',
      '231',
      '232',
      '233',
      '234',
      '235',
      '236',
      '237',
      '238',
      '239',
      '240',
      '241',
      '242',
      '243',
      '244',
      '245',
      '246',
      '248',
      '249',
      '250',
      '251',
      '252',
      '253',
      '254',
      '255',
      '256',
      '257',
      '258',
      '260',
      '261',
      '262',
      '263',
      '264',
      '265',
      '266',
      '267',
      '268',
      '269',
      '290',
      '291',
      '297',
      '298',
      '299',
      '350',
      '351',
      '352',
      '353',
      '354',
      '355',
      '356',
      '357',
      '358',
      '359',
      '370',
      '371',
      '372',
      '373',
      '374',
      '375',
      '376',
      '377',
      '378',
      '380',
      '381',
      '382',
      '383',
      '385',
      '386',
      '387',
      '389',
      '420',
      '421',
      '423',
      '500',
      '501',
      '502',
      '503',
      '504',
      '505',
      '506',
      '507',
      '508',
      '509',
      '590',
      '591',
      '592',
      '593',
      '594',
      '595',
      '596',
      '597',
      '598',
      '599',
      '670',
      '672',
      '673',
      '674',
      '675',
      '676',
      '677',
      '678',
      '679',
      '680',
      '681',
      '682',
      '683',
      '684',
      '685',
      '686',
      '687',
      '688',
      '689',
      '690',
      '691',
      '692',
      '850',
      '852',
      '853',
      '855',
      '856',
      '880',
      '886',
      '960',
      '961',
      '962',
      '963',
      '964',
      '965',
      '966',
      '967',
      '968',
      '970',
      '971',
      '972',
      '973',
      '974',
      '975',
      '976',
      '977',
      '992',
      '993',
      '994',
      '995',
      '996',
      '998'
    ];

    for (final code in commonCountryCodes) {
      if (phone.startsWith(code) && phone.length >= code.length + 7) {
        return true;
      }
    }

    return false;
  }

  /// Check if two phone numbers match using multiple variations
  /// This is more robust than simple normalization
  static bool doPhoneNumbersMatch(String phone1, String phone2) {
    if (phone1.isEmpty || phone2.isEmpty) return false;

    final variations1 = generatePhoneVariations(phone1);
    final variations2 = generatePhoneVariations(phone2);

    // Check if any variation of phone1 matches any variation of phone2
    for (final variation1 in variations1) {
      if (variations2.contains(variation1) && variation1.isNotEmpty) {
        return true;
      }
    }

    return false;
  }

  /// Normalize phone number by removing international country codes for matching
  /// This function matches the SQL normalize_phone_number function for consistency
  /// Returns the phone number without country code for consistent matching
  static String normalizePhoneForMatching(String phone) {
    final cleaned = cleanPhoneNumber(phone);
    if (cleaned.isEmpty) return '';

    // Handle country codes (ordered by length to avoid partial matches)
    // 4-digit country codes (US territories and some Caribbean)
    final fourDigitCodes = [
      '1684',
      '1264',
      '1268',
      '1242',
      '1246',
      '1441',
      '1284',
      '1345',
      '1767',
      '1809',
      '1829',
      '1849',
      '1473',
      '1671',
      '1876',
      '1664',
      '1670',
      '1787',
      '1939',
      '1869',
      '1758',
      '1784',
      '1868',
      '1649'
    ];

    for (final code in fourDigitCodes) {
      if (cleaned.startsWith(code) && cleaned.length > code.length) {
        return cleaned.substring(code.length);
      }
    }

    // 3-digit country codes
    final threeDigitCodes = [
      '355',
      '213',
      '376',
      '244',
      '374',
      '297',
      '994',
      '973',
      '880',
      '375',
      '501',
      '229',
      '975',
      '591',
      '387',
      '267',
      '673',
      '359',
      '226',
      '257',
      '855',
      '237',
      '238',
      '236',
      '235',
      '269',
      '243',
      '242',
      '682',
      '506',
      '225',
      '385',
      '599',
      '357',
      '420',
      '253',
      '593',
      '503',
      '240',
      '291',
      '372',
      '268',
      '251',
      '500',
      '298',
      '679',
      '358',
      '594',
      '689',
      '241',
      '220',
      '995',
      '233',
      '350',
      '299',
      '590',
      '502',
      '224',
      '245',
      '592',
      '509',
      '504',
      '852',
      '354',
      '964',
      '353',
      '972',
      '962',
      '686',
      '383',
      '965',
      '996',
      '856',
      '371',
      '961',
      '266',
      '231',
      '218',
      '423',
      '370',
      '352',
      '853',
      '261',
      '265',
      '960',
      '223',
      '356',
      '692',
      '596',
      '222',
      '230',
      '691',
      '373',
      '377',
      '976',
      '382',
      '212',
      '258',
      '264',
      '674',
      '977',
      '687',
      '505',
      '227',
      '234',
      '683',
      '850',
      '389',
      '968',
      '680',
      '970',
      '507',
      '675',
      '595',
      '974',
      '262',
      '250',
      '290',
      '508',
      '685',
      '378',
      '239',
      '966',
      '221',
      '381',
      '248',
      '232',
      '421',
      '386',
      '677',
      '252',
      '211',
      '249',
      '597',
      '963',
      '886',
      '992',
      '255',
      '670',
      '228',
      '690',
      '676',
      '216',
      '993',
      '688',
      '256',
      '380',
      '971',
      '598',
      '998',
      '678',
      '379',
      '681',
      '967',
      '260',
      '263',
      '351'
    ];

    for (final code in threeDigitCodes) {
      if (cleaned.startsWith(code) && cleaned.length > code.length) {
        return cleaned.substring(code.length);
      }
    }

    // 2-digit country codes
    final twoDigitCodes = [
      '93',
      '54',
      '61',
      '43',
      '32',
      '55',
      '56',
      '86',
      '57',
      '53',
      '45',
      '20',
      '33',
      '49',
      '30',
      '36',
      '91',
      '62',
      '98',
      '39',
      '81',
      '60',
      '52',
      '31',
      '64',
      '47',
      '92',
      '51',
      '63',
      '48',
      '40',
      '65',
      '27',
      '82',
      '34',
      '94',
      '46',
      '41',
      '66',
      '90',
      '44',
      '58',
      '84',
      '95'
    ];

    for (final code in twoDigitCodes) {
      if (cleaned.startsWith(code) && cleaned.length > code.length) {
        final withoutCountryCode = cleaned.substring(code.length);
        if (withoutCountryCode.length >= 7) {
          // Minimum valid phone length
          return withoutCountryCode;
        }
      }
    }

    // 1-digit country codes (handle these last to avoid conflicts)
    final oneDigitCodes = ['1', '7'];

    for (final code in oneDigitCodes) {
      if (cleaned.startsWith(code) && cleaned.length > code.length) {
        final withoutCountryCode = cleaned.substring(code.length);
        if (withoutCountryCode.length >= 7) {
          // Minimum valid phone length
          return withoutCountryCode;
        }
      }
    }

    // If no country code found or number is too short, return as-is
    return cleaned;
  }

  static Future<void> openAppSettings() async {
    await openAppSettings();
  }

  static String formatPhoneNumber(String phone) {
    final cleaned = cleanPhoneNumber(phone);
    if (cleaned.length >= 10) {
      // Format as (XXX) XXX-XXXX for US numbers
      if (cleaned.length == 10) {
        return '(${cleaned.substring(0, 3)}) ${cleaned.substring(3, 6)}-${cleaned.substring(6)}';
      } else if (cleaned.length == 11 && cleaned.startsWith('1')) {
        return '+1 (${cleaned.substring(1, 4)}) ${cleaned.substring(4, 7)}-${cleaned.substring(7)}';
      }
    }
    return phone; // Return original if can't format
  }
}
