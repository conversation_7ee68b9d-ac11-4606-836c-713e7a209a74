// ignore_for_file: avoid_print

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/models.dart' as models;
import 'supabase_service.dart';
import 'connectivity_service.dart';
import 'contacts_service.dart';

// Cache entry for category assignments
class CategoryAssignmentCache {
  final models.Category? category;
  final DateTime cachedAt;

  CategoryAssignmentCache({
    required this.category,
    required this.cachedAt,
  });

  bool get isExpired {
    // Cache expires after 10 minutes for offline scenarios
    return DateTime.now().difference(cachedAt).inMinutes > 10;
  }
}

class CategoryAssignmentService {
  static final CategoryAssignmentService _instance =
      CategoryAssignmentService._internal();
  factory CategoryAssignmentService() => _instance;
  CategoryAssignmentService._internal();

  // Cache for category assignments
  static final Map<String, CategoryAssignmentCache> _categoryAssignmentCache =
      {};
  static Timer? _cacheCleanupTimer;

  // Real-time subscription for category assignment updates
  static RealtimeChannel? _categoryAssignmentSubscription;

  // Connection monitoring
  static Timer? _connectionMonitorTimer;
  static DateTime? _lastUpdateReceived;
  static bool _isSubscriptionActive = false;

  // SharedPreferences keys for persistent category assignment cache
  static const String _categoryAssignmentCacheKey = 'category_assignment_cache';

  // Stream controller for real-time category assignment updates
  static final StreamController<Map<String, models.Category?>>
      _categoryAssignmentUpdateController =
      StreamController<Map<String, models.Category?>>.broadcast();

  // Stream for listening to category assignment updates
  static Stream<Map<String, models.Category?>>
      get categoryAssignmentUpdatesStream =>
          _categoryAssignmentUpdateController.stream;

  // Initialize the service
  static Future<void> initialize() async {
    await _loadPersistentCategoryAssignmentCache();
    _startCacheCleanup();
    _startConnectionMonitoring();
    await startRealtimeSubscription();
  }

  // Check category assignment for a specific contact (cache-first approach)
  static Future<models.Category?> checkContactCategoryAssignment({
    required String contactUserId,
    required String callerPhone,
    required List<String> contactPhoneNumbers,
  }) async {
    try {
      // Generate cache key from caller phone variations
      final phoneVariations =
          ContactsService.generatePhoneVariations(callerPhone);
      String? cacheKey;
      CategoryAssignmentCache? cachedEntry;

      // Check cache for any phone variation
      for (final variation in phoneVariations) {
        final key = '${contactUserId}_$variation';
        final entry = _categoryAssignmentCache[key];
        if (entry != null && !entry.isExpired) {
          cacheKey = key;
          cachedEntry = entry;
          break;
        }
      }

      if (cachedEntry != null) {
        print(
            '📱 Using cached category assignment for contact $contactUserId: ${cachedEntry.category?.type.name ?? 'none'}');
        return cachedEntry.category;
      }

      print(
          '🔄 Fetching fresh category assignment for contact $contactUserId from server');

      final connectivityService = ConnectivityService();
      if (!connectivityService.isOnline) {
        print('📱 Offline - checking cached and persistent data...');

        // First try cached data (even if expired)
        if (cachedEntry?.category != null) {
          final category = cachedEntry!.category!;
          print(
              '📱 Using cached category assignment offline: ${category.type.name}');
          return category;
        }

        // If no cached data, try to load from persistent storage
        print('📱 No cached data found, loading from persistent storage...');
        await _loadPersistentCategoryAssignmentCache();

        // Check cache again after loading from persistent storage
        for (final variation in phoneVariations) {
          final key = '${contactUserId}_$variation';
          final entry = _categoryAssignmentCache[key];
          if (entry?.category != null) {
            print(
                '📱 Found category in persistent storage: ${entry!.category!.type.name}');
            return entry.category;
          }
        }

        print('📱 No category assignment found in cache or persistent storage');
        return null;
      }

      // Fetch from server
      final category = await SupabaseService.getContactCategory(
        contactUserId: contactUserId,
        callerPhone: callerPhone,
      );

      // Cache the result for all phone variations
      final cacheTime = DateTime.now();
      for (final variation in phoneVariations) {
        final key = '${contactUserId}_$variation';
        _categoryAssignmentCache[key] = CategoryAssignmentCache(
          category: category,
          cachedAt: cacheTime,
        );
      }

      // Save to persistent cache for offline access
      await _savePersistentCategoryAssignmentCache();

      // Emit category assignment update for real-time UI updates
      _emitCategoryAssignmentUpdate(callerPhone, category);

      print(
          '✅ Category assignment for contact $contactUserId: ${category?.type.name ?? 'none'}');
      return category;
    } catch (e) {
      print('❌ Failed to check contact category assignment: $e');
      // Return cached data if available
      final phoneVariations =
          ContactsService.generatePhoneVariations(callerPhone);
      for (final variation in phoneVariations) {
        final key = '${contactUserId}_$variation';
        final cachedEntry = _categoryAssignmentCache[key];
        if (cachedEntry != null) {
          return cachedEntry.category;
        }
      }
      return null;
    }
  }

  // Get cached category assignment without making network calls
  static models.Category? getCachedCategoryAssignment({
    required String contactUserId,
    required String callerPhone,
  }) {
    final phoneVariations =
        ContactsService.generatePhoneVariations(callerPhone);

    for (final variation in phoneVariations) {
      final key = '${contactUserId}_$variation';
      final cachedEntry = _categoryAssignmentCache[key];
      if (cachedEntry != null) {
        // Return cached data even if expired when checking cache only
        return cachedEntry.category;
      }
    }

    return null;
  }

  // Get cached category assignment with persistent storage fallback (offline-friendly)
  static Future<models.Category?> getCachedCategoryAssignmentWithFallback({
    required String contactUserId,
    required String callerPhone,
  }) async {
    // First check in-memory cache
    final cachedCategory = getCachedCategoryAssignment(
      contactUserId: contactUserId,
      callerPhone: callerPhone,
    );

    if (cachedCategory != null) {
      return cachedCategory;
    }

    // If not in memory, try loading from persistent storage
    await _loadPersistentCategoryAssignmentCache();

    // Check cache again after loading from persistent storage
    return getCachedCategoryAssignment(
      contactUserId: contactUserId,
      callerPhone: callerPhone,
    );
  }

  // Proactively fetch and cache category assignments for multiple contacts
  static Future<void> proactivelyFetchCategoryAssignments({
    required String callerPhone,
    required List<String> contactUserIds,
  }) async {
    final connectivityService = ConnectivityService();
    if (!connectivityService.isOnline) {
      print(
          '📱 Device is offline - skipping proactive category assignments fetch');
      return;
    }

    try {
      print(
          '📱 Proactively fetching category assignments for ${contactUserIds.length} contacts...');

      // Fetch category assignments for each contact in parallel (but limit concurrency)
      final futures = <Future<void>>[];
      for (final contactUserId in contactUserIds) {
        final future = checkContactCategoryAssignment(
          contactUserId: contactUserId,
          callerPhone: callerPhone,
          contactPhoneNumbers: [], // Not needed for server fetch
        );
        futures.add(future);

        // Limit concurrent requests to avoid overwhelming the server
        if (futures.length >= 5) {
          await Future.wait(futures);
          futures.clear();
          // Small delay to be respectful to the server
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }

      // Wait for remaining requests
      if (futures.isNotEmpty) {
        await Future.wait(futures);
      }

      print(
          '📱 Completed proactive category assignments fetch for all contacts');
    } catch (e) {
      print('❌ Failed to proactively fetch category assignments: $e');
    }
  }

  // Emit category assignment update for real-time UI updates
  static void _emitCategoryAssignmentUpdate(
      String callerPhone, models.Category? category) {
    try {
      if (!_categoryAssignmentUpdateController.isClosed) {
        _categoryAssignmentUpdateController.add({callerPhone: category});
        print(
            '📱 Emitted category assignment update for phone $callerPhone: ${category?.type.name ?? 'none'}');
      }
    } catch (e) {
      print('❌ Failed to emit category assignment update: $e');
    }
  }

  // Clear category assignment cache for a specific contact
  static void clearCacheForContact(String contactUserId, String callerPhone) {
    final phoneVariations =
        ContactsService.generatePhoneVariations(callerPhone);
    for (final variation in phoneVariations) {
      final key = '${contactUserId}_$variation';
      _categoryAssignmentCache.remove(key);
    }
    print('🗑️ Cleared category assignment cache for contact $contactUserId');
  }

  // Clear all category assignment cache
  static void clearAllCache() {
    _categoryAssignmentCache.clear();
    print('🗑️ Cleared all category assignment cache');
  }

  // Load persistent category assignment cache from SharedPreferences
  static Future<void> _loadPersistentCategoryAssignmentCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheDataString = prefs.getString(_categoryAssignmentCacheKey);

      if (cacheDataString != null) {
        final cacheData = jsonDecode(cacheDataString) as Map<String, dynamic>;

        for (final entry in cacheData.entries) {
          final cacheKey = entry.key;
          final assignmentData = entry.value as Map<String, dynamic>;

          models.Category? category;
          if (assignmentData['category'] != null) {
            final categoryData =
                assignmentData['category'] as Map<String, dynamic>;
            category = models.Category.fromJson(categoryData);
          }

          final cachedAt = DateTime.parse(assignmentData['cachedAt'] as String);

          _categoryAssignmentCache[cacheKey] = CategoryAssignmentCache(
            category: category,
            cachedAt: cachedAt,
          );
        }

        print(
            '📱 Loaded ${_categoryAssignmentCache.length} category assignments from persistent cache');

        // Debug: Show what was loaded
        for (final entry in _categoryAssignmentCache.entries) {
          final cacheEntry = entry.value;
          print(
              '📱 Cached: ${entry.key} -> ${cacheEntry.category?.type.name ?? 'null'} (${cacheEntry.isExpired ? 'expired' : 'valid'})');
        }
      }
    } catch (e) {
      print('❌ Failed to load persistent category assignment cache: $e');
    }
  }

  // Save persistent category assignment cache to SharedPreferences
  static Future<void> _savePersistentCategoryAssignmentCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheData = <String, dynamic>{};

      for (final entry in _categoryAssignmentCache.entries) {
        cacheData[entry.key] = {
          'category': entry.value.category?.toJson(),
          'cachedAt': entry.value.cachedAt.toIso8601String(),
        };
      }

      await prefs.setString(_categoryAssignmentCacheKey, jsonEncode(cacheData));
      print(
          '📱 Saved ${_categoryAssignmentCache.length} category assignments to persistent cache');
    } catch (e) {
      print('❌ Failed to save persistent category assignment cache: $e');
    }
  }

  // Start cache cleanup timer
  static void _startCacheCleanup() {
    _cacheCleanupTimer?.cancel();
    _cacheCleanupTimer = Timer.periodic(const Duration(minutes: 10), (timer) {
      final expiredKeys = <String>[];

      for (final entry in _categoryAssignmentCache.entries) {
        if (entry.value.isExpired) {
          expiredKeys.add(entry.key);
        }
      }

      for (final key in expiredKeys) {
        _categoryAssignmentCache.remove(key);
      }

      if (expiredKeys.isNotEmpty) {
        print(
            '🗑️ Cleaned up ${expiredKeys.length} expired category assignment cache entries');
      }
    });
  }

  // Start real-time subscription for category assignment updates
  static Future<void> startRealtimeSubscription() async {
    try {
      final connectivityService = ConnectivityService();
      if (!connectivityService.isOnline) {
        print(
            '📱 Device is offline - skipping category assignment subscription');
        return;
      }

      print('📱 Starting real-time category assignment subscription...');

      // Remove existing subscription if any
      await stopRealtimeSubscription();

      // Create new subscription to user_contacts table with connection monitoring
      _categoryAssignmentSubscription = SupabaseService.client
          .channel('category_assignment_updates')
          .onPostgresChanges(
            event: PostgresChangeEvent.insert,
            schema: 'public',
            table: 'user_contacts',
            callback: (payload) {
              print(
                  '📱 Real-time category assignment insert: ${payload.newRecord}');
              _lastUpdateReceived = DateTime.now();
              _handleRealtimeCategoryAssignmentUpdate(payload);
            },
          )
          .onPostgresChanges(
            event: PostgresChangeEvent.update,
            schema: 'public',
            table: 'user_contacts',
            callback: (payload) {
              print(
                  '📱 Real-time category assignment update: ${payload.newRecord}');
              _lastUpdateReceived = DateTime.now();
              _handleRealtimeCategoryAssignmentUpdate(payload);
            },
          )
          .onPostgresChanges(
            event: PostgresChangeEvent.delete,
            schema: 'public',
            table: 'user_contacts',
            callback: (payload) {
              print(
                  '📱 Real-time category assignment delete: ${payload.oldRecord}');
              _lastUpdateReceived = DateTime.now();
              _handleRealtimeCategoryAssignmentDelete(payload);
            },
          )
          .subscribe();

      _isSubscriptionActive = true;
      _lastUpdateReceived = DateTime.now();
      print(
          '✅ Real-time category assignment subscription started with monitoring');
    } catch (e) {
      print('❌ Failed to start real-time category assignment subscription: $e');
    }
  }

  // Stop real-time subscription
  static Future<void> stopRealtimeSubscription() async {
    try {
      if (_categoryAssignmentSubscription != null) {
        await SupabaseService.client
            .removeChannel(_categoryAssignmentSubscription!);
        _categoryAssignmentSubscription = null;
        _isSubscriptionActive = false;
        print('📱 Real-time category assignment subscription stopped');
      }
    } catch (e) {
      print('❌ Failed to stop real-time category assignment subscription: $e');
    }
  }

  // Start connection monitoring to detect and fix disconnections
  static void _startConnectionMonitoring() {
    _connectionMonitorTimer?.cancel();
    _connectionMonitorTimer =
        Timer.periodic(const Duration(minutes: 2), (timer) {
      _checkConnectionHealth();
    });
    print('📱 Started category assignment connection monitoring');
  }

  // Check connection health and reconnect if needed
  static Future<void> _checkConnectionHealth() async {
    try {
      final connectivityService = ConnectivityService();
      if (!connectivityService.isOnline) {
        return; // Skip check if offline
      }

      // Test the connection with a simple query
      bool connectionWorking = await _testConnection();

      if (!connectionWorking) {
        print('⚠️ Category assignment connection test failed');
        print('🔄 Attempting to reconnect category assignment subscription...');

        // Force reconnection
        await stopRealtimeSubscription();
        await Future.delayed(const Duration(seconds: 2)); // Brief delay
        await startRealtimeSubscription();

        print('✅ Category assignment subscription reconnection attempted');
        return;
      }

      // Check if subscription is supposed to be active but hasn't received updates recently
      if (_isSubscriptionActive && _lastUpdateReceived != null) {
        final timeSinceLastUpdate =
            DateTime.now().difference(_lastUpdateReceived!);

        // If no updates for 15 minutes and we're online, the connection might be stale
        if (timeSinceLastUpdate.inMinutes > 15) {
          print(
              '⚠️ Category assignment subscription appears stale (${timeSinceLastUpdate.inMinutes} min since last update)');
          print(
              '🔄 Attempting to reconnect category assignment subscription...');

          // Force reconnection
          await stopRealtimeSubscription();
          await Future.delayed(const Duration(seconds: 2)); // Brief delay
          await startRealtimeSubscription();

          print('✅ Category assignment subscription reconnection attempted');
        }
      }
    } catch (e) {
      print('❌ Error during category assignment connection health check: $e');
    }
  }

  // Test connection with a simple query
  static Future<bool> _testConnection() async {
    try {
      // Simple query to test if Supabase connection is working
      await SupabaseService.client
          .from('user_contacts')
          .select('id')
          .limit(1)
          .timeout(const Duration(seconds: 5));

      print('📱 Category assignment connection test: OK');
      return true;
    } catch (e) {
      print('❌ Category assignment connection test failed: $e');
      return false;
    }
  }

  // Force refresh subscription (useful for debugging)
  static Future<void> forceRefreshSubscription() async {
    print('🔄 Force refreshing category assignment subscription...');
    await stopRealtimeSubscription();
    await Future.delayed(const Duration(seconds: 1));
    await startRealtimeSubscription();
    print('✅ Category assignment subscription force refresh completed');
  }

  // Handle real-time category assignment updates (insert/update)
  static void _handleRealtimeCategoryAssignmentUpdate(
      PostgresChangePayload payload) {
    try {
      final contactPhone =
          payload.newRecord['categorized_contact_phone'] as String?;
      final contactUserId = payload.newRecord['user_id'] as String?;

      if (contactPhone == null || contactUserId == null) return;

      print(
          '📱 Processing real-time category assignment update for phone: $contactPhone');

      // Clear the cache for this contact to force a fresh fetch
      clearCacheForContact(contactUserId, contactPhone);

      // Proactively fetch fresh data to update cache immediately
      _refreshSpecificCacheEntry(contactUserId, contactPhone);

      // Emit update to trigger UI refresh
      _emitCategoryAssignmentUpdate(contactPhone, null);

      print(
          '✅ Real-time category assignment update processed for $contactPhone');
    } catch (e) {
      print('❌ Failed to handle real-time category assignment update: $e');
    }
  }

  // Refresh a specific cache entry in the background
  static void _refreshSpecificCacheEntry(
      String contactUserId, String contactPhone) {
    // Run in background without blocking
    Future(() async {
      try {
        final connectivityService = ConnectivityService();
        if (!connectivityService.isOnline) return;

        print(
            '🔄 Refreshing specific cache entry for contact $contactUserId, phone $contactPhone');

        // Fetch fresh data (this will cache it again)
        final freshCategory = await checkContactCategoryAssignment(
          contactUserId: contactUserId,
          callerPhone: contactPhone,
          contactPhoneNumbers: [],
        );

        if (freshCategory != null) {
          print('✅ Refreshed specific cache entry: ${freshCategory.type.name}');
        } else {
          print('📱 No category found for specific cache refresh');
        }
      } catch (e) {
        print('⚠️ Failed to refresh specific cache entry: $e');
      }
    });
  }

  // Handle real-time category assignment deletions
  static void _handleRealtimeCategoryAssignmentDelete(
      PostgresChangePayload payload) {
    try {
      final contactPhone =
          payload.oldRecord['categorized_contact_phone'] as String?;
      final contactUserId = payload.oldRecord['user_id'] as String?;

      if (contactPhone == null || contactUserId == null) return;

      print(
          '📱 Processing real-time category assignment deletion for phone: $contactPhone');

      // Clear cache for this contact
      clearCacheForContact(contactUserId, contactPhone);

      // Emit update to indicate assignment was removed
      _emitCategoryAssignmentUpdate(contactPhone, null);

      print(
          '✅ Real-time category assignment deletion processed for $contactPhone');
    } catch (e) {
      print('❌ Failed to handle real-time category assignment deletion: $e');
    }
  }

  // Handle connectivity changes
  static Future<void> onConnectivityChanged(bool isOnline) async {
    if (isOnline) {
      print(
          '📱 Device came online - starting real-time category assignment subscription');
      await startRealtimeSubscription();
      // Refresh cache when connectivity is restored to get latest updates
      await refreshCacheOnConnectivity();
    } else {
      print(
          '📱 Device went offline - stopping real-time category assignment subscription');
      await stopRealtimeSubscription();
    }
  }

  // Refresh category assignment cache when connectivity is restored
  static Future<void> refreshCacheOnConnectivity() async {
    try {
      final connectivityService = ConnectivityService();
      if (!connectivityService.isOnline) {
        print('📱 Still offline - cannot refresh category assignment cache');
        return;
      }

      print(
          '🔄 Refreshing category assignment cache after connectivity restored...');

      // Get all cached contact-phone combinations
      final cacheKeys = _categoryAssignmentCache.keys.toList();
      int refreshedCount = 0;

      // Refresh each cached assignment
      for (final cacheKey in cacheKeys) {
        try {
          // Parse cache key to extract contactUserId and callerPhone
          final parts = cacheKey.split('_');
          if (parts.length >= 2) {
            final contactUserId = parts[0];
            final callerPhone = parts.sublist(1).join('_');

            print(
                '🔄 Refreshing cache for contact $contactUserId, phone $callerPhone');

            // Remove from cache to force fresh fetch
            _categoryAssignmentCache.remove(cacheKey);

            // Fetch fresh data (this will cache it again)
            final freshCategory = await checkContactCategoryAssignment(
              contactUserId: contactUserId,
              callerPhone: callerPhone,
              contactPhoneNumbers: [],
            );

            if (freshCategory != null) {
              print(
                  '✅ Refreshed category for $contactUserId: ${freshCategory.type.name}');
              refreshedCount++;
            } else {
              print('📱 No category found for $contactUserId after refresh');
            }
          }
        } catch (e) {
          print('⚠️ Failed to refresh cache for key $cacheKey: $e');
          // Continue with other cache entries even if one fails
        }

        // Small delay to avoid overwhelming the server
        await Future.delayed(const Duration(milliseconds: 100));
      }

      // Save refreshed cache to persistent storage
      await _savePersistentCategoryAssignmentCache();

      print(
          '✅ Category assignment cache refresh completed: $refreshedCount entries refreshed');
    } catch (e) {
      print('❌ Failed to refresh category assignment cache: $e');
    }
  }

  static void dispose() {
    _categoryAssignmentUpdateController.close();
    _cacheCleanupTimer?.cancel();
    _connectionMonitorTimer?.cancel();
    stopRealtimeSubscription();
  }
}
