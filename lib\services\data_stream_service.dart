// ignore_for_file: avoid_print

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/models.dart' as models;
import 'offline_contact_service.dart';
import 'contacts_service.dart';
import 'emergency_mode_service.dart';
import 'category_assignment_service.dart';
import 'assigned_categories_service.dart';
import 'connectivity_service.dart';
import 'supabase_service.dart';
import 'performance_monitor_service.dart';

/// Service that manages real-time data streams for the contacts app
/// Provides instant UI updates and efficient data synchronization
class DataStreamService extends ChangeNotifier {
  static final DataStreamService _instance = DataStreamService._internal();
  factory DataStreamService() => _instance;
  DataStreamService._internal();

  final OfflineContactService _offlineService = OfflineContactService();
  final PerformanceMonitorService _performanceMonitor =
      PerformanceMonitorService();

  // Combined data streams for UI consumption
  final StreamController<List<ContactWithProfile>> _contactsStreamController =
      StreamController<List<ContactWithProfile>>.broadcast();
  final StreamController<DataLoadingState> _loadingStateController =
      StreamController<DataLoadingState>.broadcast();
  final StreamController<String?> _errorController =
      StreamController<String?>.broadcast();

  // Public streams
  Stream<List<ContactWithProfile>> get contactsStream =>
      _contactsStreamController.stream;
  Stream<DataLoadingState> get loadingStateStream =>
      _loadingStateController.stream;
  Stream<String?> get errorStream => _errorController.stream;

  // Current data state
  List<ContactWithProfile> _currentContacts = [];
  DataLoadingState _currentLoadingState = DataLoadingState.initial;
  String? _currentError;

  // Getters for current state
  List<ContactWithProfile> get currentContacts => List.from(_currentContacts);
  DataLoadingState get currentLoadingState => _currentLoadingState;
  String? get currentError => _currentError;

  bool _isInitialized = false;
  StreamSubscription? _profilesSubscription;
  StreamSubscription? _categoriesSubscription;
  StreamSubscription? _assignmentsSubscription;
  StreamSubscription? _syncStatusSubscription;
  StreamSubscription<Map<String, EmergencyStatus?>>?
      _emergencyStatusSubscription;

  // Real-time sync timer
  Timer? _realTimeSyncTimer;
  static const Duration _realTimeSyncInterval = Duration(seconds: 30);

  // Device contacts cache to avoid expensive device contact reads
  List<models.DeviceContact>? _cachedDeviceContacts;
  DateTime? _deviceContactsCacheTime;
  static const Duration _deviceContactsCacheMaxAge =
      Duration(minutes: 15); // Reasonable cache duration for device contacts

  // Contact change detection
  int? _lastKnownContactCount;
  Timer? _contactChangeDetectionTimer;
  static const Duration _contactCheckInterval =
      Duration(minutes: 2); // Check every 2 minutes

  Future<void> initialize() async {
    if (_isInitialized) return;

    print('📱 Initializing DataStreamService...');

    try {
      // Initialize offline service first
      await _offlineService.initialize();

      // Subscribe to data streams
      _subscribeToDataStreams();

      // Preload device contacts cache in background for faster access
      _preloadDeviceContactsCache();

      // Start real-time sync
      _startRealTimeSync();

      // Start contact change detection
      _startContactChangeDetection();

      // Setup connectivity listener for emergency status refresh
      _setupConnectivityListener();

      // Load initial data (cache-first approach)
      await loadContactsData(forceRefresh: false);

      _isInitialized = true;
      print('✅ DataStreamService initialized successfully');
    } catch (e) {
      print('❌ Failed to initialize DataStreamService: $e');
      _emitError('Failed to initialize data service: $e');
    }
  }

  void _subscribeToDataStreams() {
    // Listen to profiles updates
    _profilesSubscription = _offlineService.profilesStream.listen(
      (profiles) {
        print('📱 Received profiles update: ${profiles.length} profiles');
        _rebuildContactsList();
      },
      onError: (error) {
        print('❌ Profiles stream error: $error');
        _emitError('Failed to load profiles: $error');
      },
    );

    // Listen to categories updates
    _categoriesSubscription = _offlineService.categoriesStream.listen(
      (categories) {
        print('📱 Received categories update: ${categories.length} categories');
        _rebuildContactsList();
      },
      onError: (error) {
        print('❌ Categories stream error: $error');
        _emitError('Failed to load categories: $error');
      },
    );

    // Listen to contact assignments updates
    _assignmentsSubscription = _offlineService.contactAssignmentsStream.listen(
      (assignments) {
        print(
            '📱 Received assignments update: ${assignments.length} assignments');
        _rebuildContactsList();
      },
      onError: (error) {
        print('❌ Assignments stream error: $error');
        _emitError('Failed to load assignments: $error');
      },
    );

    // Listen to emergency status updates
    _emergencyStatusSubscription =
        EmergencyModeService.emergencyStatusUpdateStream.listen(
      (emergencyUpdates) {
        print(
            '📱 Received emergency status updates: ${emergencyUpdates.length} updates');
        for (final entry in emergencyUpdates.entries) {
          final userId = entry.key;
          final emergencyStatus = entry.value;
          updateContactEmergencyStatus(userId, emergencyStatus);
        }
      },
      onError: (error) {
        print('❌ Emergency status stream error: $error');
      },
    );
  }

  Future<void> loadContactsData({bool forceRefresh = false}) async {
    if (!_canUseService()) return;

    _performanceMonitor.startOperation('load_contacts_data');
    try {
      _emitLoadingState(DataLoadingState.loading);
      _emitError(null); // Clear any previous errors

      print('📱 Loading contacts data...');

      // Check if user is authenticated
      final currentUser = SupabaseService.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated. Please log in again.');
      }

      if (forceRefresh) {
        // Force refresh - invalidate caches (used by refresh button)
        print('📱 Force refresh requested - invalidating caches');
        await _offlineService.refreshCache();
        // Also clear device contacts cache to fetch newly added contacts
        await clearDeviceContactsCache();
      } else {
        // Cache-first approach - use existing cache (used by navigation)
        print('📱 Using cache-first approach - preserving existing cache');
        await _offlineService.loadCachedDataOnly();
      }

      // Trigger smart background sync if online
      if (_offlineService.isOnline) {
        print('📱 Starting smart background sync...');
        // Use fire-and-forget for background sync
        _offlineService.smartSync().catchError((error) {
          print('❌ Background sync failed: $error');
          // Don't emit error for background sync failures
        });
      }

      // Build contacts list from current cached data
      await _rebuildContactsList();

      _emitLoadingState(DataLoadingState.loaded);
      _performanceMonitor.endOperation('load_contacts_data');
      print('✅ Contacts data loaded successfully');
    } catch (e) {
      print('❌ Error loading contacts data: $e');
      _performanceMonitor.endOperation('load_contacts_data');
      _emitLoadingState(DataLoadingState.error);
      _emitError(e.toString());
    }
  }

  /// Get cached device contacts or fetch them if cache is stale
  Future<List<models.DeviceContact>> _getCachedDeviceContacts() async {
    final now = DateTime.now();

    // First check in-memory cache
    if (_cachedDeviceContacts != null &&
        _deviceContactsCacheTime != null &&
        now.difference(_deviceContactsCacheTime!) <
            _deviceContactsCacheMaxAge) {
      print(
          '📱 Using in-memory cached device contacts (${_cachedDeviceContacts!.length} contacts)');
      return _cachedDeviceContacts!;
    }

    // Check persistent cache (SharedPreferences)
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString('device_contacts_cache');
      final cacheTimeStr = prefs.getString('device_contacts_cache_time');

      if (cachedJson != null && cacheTimeStr != null) {
        final cacheTime = DateTime.parse(cacheTimeStr);
        if (now.difference(cacheTime) < _deviceContactsCacheMaxAge) {
          print('📱 Loading device contacts from persistent cache...');
          final List<dynamic> contactsJson = jsonDecode(cachedJson);
          final deviceContacts = contactsJson
              .map((json) => models.DeviceContact(
                    id: json['id'],
                    displayName: json['displayName'] ?? '',
                    phoneNumbers: List<String>.from(json['phoneNumbers'] ?? []),
                    email: json['email'],
                    avatar: json['avatar'],
                  ))
              .toList();

          // Update in-memory cache
          _cachedDeviceContacts = deviceContacts;
          _deviceContactsCacheTime = cacheTime;

          print(
              '📱 Loaded ${deviceContacts.length} device contacts from persistent cache');
          return deviceContacts;
        } else {
          print('📱 Persistent cache is stale, will fetch fresh data');
        }
      }
    } catch (e) {
      print('⚠️ Error loading persistent cache: $e');
    }

    // Cache is stale or empty, fetch fresh data
    try {
      print('📱 Fetching fresh device contacts...');
      final deviceContacts = await ContactsService.getDeviceContacts();

      // Update in-memory cache
      _cachedDeviceContacts = deviceContacts;
      _deviceContactsCacheTime = now;

      // Update persistent cache
      await _saveToPersistentCache(deviceContacts, now);

      print('📱 Cached ${deviceContacts.length} device contacts');
      return deviceContacts;
    } catch (e) {
      print('⚠️ Could not access device contacts: $e');
      // Return empty list if we can't access device contacts
      return [];
    }
  }

  /// Save device contacts to persistent cache
  Future<void> _saveToPersistentCache(
      List<models.DeviceContact> contacts, DateTime cacheTime) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Convert contacts to JSON
      final contactsJson = contacts
          .map((contact) => {
                'id': contact.id,
                'displayName': contact.displayName,
                'phoneNumbers': contact.phoneNumbers,
                'email': contact.email,
                'avatar': contact.avatar,
              })
          .toList();

      // Save to SharedPreferences
      await prefs.setString('device_contacts_cache', jsonEncode(contactsJson));
      await prefs.setString(
          'device_contacts_cache_time', cacheTime.toIso8601String());

      print('📱 Saved ${contacts.length} device contacts to persistent cache');
    } catch (e) {
      print('⚠️ Error saving to persistent cache: $e');
    }
  }

  Future<void> _rebuildContactsList() async {
    if (!_canUseService()) return;

    _performanceMonitor.startOperation('rebuild_contacts_list');
    try {
      final currentUser = SupabaseService.currentUser;
      if (currentUser == null) {
        _performanceMonitor.endOperation('rebuild_contacts_list');
        return;
      }

      print('📱 Rebuilding contacts list...');

      // Get cached data from offline service
      final profiles = _offlineService.cachedProfiles;

      // Remove current user from profiles
      final otherProfiles =
          profiles.where((profile) => profile.id != currentUser.id).toList();

      // Get cached device contacts (this is the expensive operation we're optimizing)
      final deviceContacts = await _getCachedDeviceContacts();
      print('📱 Device contacts found: ${deviceContacts.length}');

      // Build contacts with profiles - SHOWING ONLY DEVICE CONTACTS
      final contactsWithProfiles = <ContactWithProfile>[];

      // Process only device contacts and check if they have app profiles
      for (final deviceContact in deviceContacts) {
        try {
          // Find matching app profile for this device contact
          models.Profile? matchingProfile;

          for (final profile in otherProfiles) {
            final profileVariations =
                ContactsService.generatePhoneVariations(profile.phoneNumber);

            bool foundMatch = false;
            for (final devicePhone in deviceContact.phoneNumbers) {
              final deviceVariations =
                  ContactsService.generatePhoneVariations(devicePhone);
              for (final deviceVariation in deviceVariations) {
                if (profileVariations.contains(deviceVariation) &&
                    deviceVariation.isNotEmpty) {
                  matchingProfile = profile;
                  foundMatch = true;
                  break;
                }
              }
              if (foundMatch) break;
            }
            if (foundMatch) break;
          }

          // Get cached emergency status for this contact if they have a profile
          EmergencyStatus? emergencyStatus;
          if (matchingProfile != null) {
            try {
              emergencyStatus = EmergencyModeService.getCachedEmergencyStatus(
                  matchingProfile.id);
            } catch (e) {
              print(
                  '⚠️ Failed to get emergency status for ${matchingProfile.fullName}: $e');
            }
          }

          // Add device contact with or without profile and emergency status
          contactsWithProfiles.add(ContactWithProfile(
            deviceContact: deviceContact,
            profile: matchingProfile,
            emergencyStatus: emergencyStatus,
          ));

          if (matchingProfile != null) {
            print(
                '📱 Added device contact with app account: ${deviceContact.displayName}');
          } else {
            print(
                '📱 Added device contact without app account: ${deviceContact.displayName}');
          }
        } catch (e) {
          print(
              '⚠️ Error processing device contact ${deviceContact.displayName}: $e');
        }
      }

      print('📱 Built ${contactsWithProfiles.length} contacts');

      // Update current contacts and emit
      _currentContacts = contactsWithProfiles;

      // Safely emit to stream
      if (!_contactsStreamController.isClosed) {
        _contactsStreamController.add(List.from(_currentContacts));
      }

      // Proactively fetch emergency statuses for all contacts when online
      final connectivityService = ConnectivityService();
      if (connectivityService.isOnline) {
        print(
            '📱 Device is online - starting proactive emergency status fetch...');
        // Run in background without blocking the UI
        _proactivelyFetchEmergencyStatuses().catchError((error) {
          print('⚠️ Background emergency status fetch failed: $error');
        });
        _proactivelyFetchCategoryAssignments().catchError((error) {
          print('⚠️ Background category assignments fetch failed: $error');
        });
      } else {
        print('📱 Device is offline - skipping proactive data fetch');
      }

      _performanceMonitor.endOperation('rebuild_contacts_list');
    } catch (e) {
      print('❌ Error rebuilding contacts list: $e');
      _performanceMonitor.endOperation('rebuild_contacts_list');
      _emitError('Failed to rebuild contacts list: $e');
    }
  }

  // Update a single contact assignment in real-time
  void updateContactAssignment(String contactPhone, models.Category? category) {
    _offlineService.updateContactAssignment(contactPhone, category);
    // The stream subscription will automatically trigger a rebuild
  }

  // Assign contact to category with immediate UI update
  Future<void> assignContactToCategory({
    required String userId,
    required String contactPhone,
    required String categoryId,
  }) async {
    if (!_canUseService()) return;

    try {
      await _offlineService.assignContactToCategory(
        userId: userId,
        contactPhone: contactPhone,
        categoryId: categoryId,
      );
      // The stream subscription will automatically update the UI
    } catch (e) {
      print('❌ Error assigning contact to category: $e');
      _emitError('Failed to assign contact: $e');
    }
  }

  // Enhanced assignment for multiple phone numbers with immediate UI update
  Future<void> assignCategoryToContactWithAllPhones({
    required String userAId,
    required List<String> userBPhoneNumbers,
    required String categoryId,
  }) async {
    if (!_canUseService()) return;

    try {
      await _offlineService.assignCategoryToContactWithAllPhones(
        userAId: userAId,
        userBPhoneNumbers: userBPhoneNumbers,
        categoryId: categoryId,
      );
      // The stream subscription will automatically update the UI
    } catch (e) {
      print('❌ Error in enhanced contact assignment: $e');
      _emitError('Failed to assign contact with all phones: $e');
    }
  }

  // Removed _getLastSyncTime - now using smart caching

  void _emitLoadingState(DataLoadingState state) {
    if (!_canUseService()) return;

    _currentLoadingState = state;
    if (!_loadingStateController.isClosed) {
      _loadingStateController.add(state);
    }
  }

  void _emitError(String? error) {
    if (!_canUseService()) return;

    _currentError = error;
    if (!_errorController.isClosed) {
      _errorController.add(error);
    }
  }

  void _startRealTimeSync() {
    // Listen to sync status for real-time updates
    _syncStatusSubscription = _offlineService.syncStatusStream.listen(
      (isSyncing) {
        if (!isSyncing) {
          // Sync completed, rebuild contacts list
          print('📱 Sync completed, rebuilding contacts list...');
          _rebuildContactsList();
        }
      },
      onError: (error) {
        print('❌ Sync status stream error: $error');
      },
    );

    // Start periodic smart sync for real-time updates
    _realTimeSyncTimer = Timer.periodic(_realTimeSyncInterval, (timer) {
      if (_offlineService.isOnline && !_offlineService.isSyncing) {
        print('📱 Performing real-time smart sync...');
        _offlineService.smartSync().catchError((error) {
          print('❌ Real-time sync failed: $error');
          // Don't emit error for background sync failures
        });
      }
    });

    print('✅ Real-time sync started');
  }

  void _setupConnectivityListener() {
    // Add callback for when connectivity is restored
    final connectivityService = ConnectivityService();
    connectivityService.addOnlineCallback(() {
      print('📱 Connectivity restored - restarting real-time subscriptions');
      // Restart real-time emergency status subscription
      EmergencyModeService.startRealtimeSubscription();
      // Restart real-time category assignments subscription and refresh cache
      CategoryAssignmentService.onConnectivityChanged(true);
      // Restart real-time assigned categories subscription and refresh cache
      AssignedCategoriesService.onConnectivityChanged(true);
      // Refresh emergency status cache when connectivity is restored
      EmergencyModeService.refreshCacheOnConnectivity();
      // Proactively fetch emergency status and category assignments for all contacts
      _proactivelyFetchEmergencyStatuses();
      _proactivelyFetchCategoryAssignments();
    });

    // Add callback for when connectivity is lost
    connectivityService.addOfflineCallback(() {
      print('📱 Connectivity lost - stopping real-time subscriptions');
      // Stop real-time subscriptions when offline
      CategoryAssignmentService.onConnectivityChanged(false);
      AssignedCategoriesService.onConnectivityChanged(false);
    });
  }

  void _stopRealTimeSync() {
    _realTimeSyncTimer?.cancel();
    _realTimeSyncTimer = null;
    _syncStatusSubscription?.cancel();
    _syncStatusSubscription = null;
    print('📱 Real-time sync stopped');
  }

  // Force a real-time sync now
  Future<void> forceSync() async {
    try {
      print('📱 Forcing immediate smart sync...');
      _emitLoadingState(DataLoadingState.loading);

      // Invalidate all caches to force fresh data
      _offlineService.invalidateAllCaches();
      await _offlineService.smartSync();

      _emitLoadingState(DataLoadingState.loaded);
      print('✅ Force sync completed');
    } catch (e) {
      print('❌ Force sync failed: $e');
      _emitLoadingState(DataLoadingState.error);
      _emitError('Sync failed: $e');
    }
  }

  // Track if service is disposed
  bool _isDisposed = false;
  bool get isDisposed => _isDisposed;

  /// Clear device contacts cache (useful when contacts permission changes)
  Future<void> clearDeviceContactsCache() async {
    print('📱 Clearing device contacts cache');
    _cachedDeviceContacts = null;
    _deviceContactsCacheTime = null;

    // Also clear persistent cache
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('device_contacts_cache');
      await prefs.remove('device_contacts_cache_time');
      print('📱 Cleared persistent device contacts cache');
    } catch (e) {
      print('⚠️ Error clearing persistent cache: $e');
    }
  }

  /// Force refresh device contacts and rebuild contacts list
  Future<void> refreshDeviceContacts() async {
    await clearDeviceContactsCache();
    await _rebuildContactsList();
  }

  /// Public method to force refresh all data including device contacts
  Future<void> forceRefreshAll() async {
    print('📱 Force refreshing all data including device contacts...');
    await loadContactsData(forceRefresh: true);
  }

  /// Manual refresh triggered by user (refresh button)
  /// This method combines full refresh with immediate contact change detection
  Future<void> manualRefresh() async {
    print('📱 Manual refresh triggered by user...');

    try {
      // First, perform immediate contact change detection
      print('📱 Checking for contact changes before full refresh...');
      await _checkForContactChanges();

      // Then perform full refresh to ensure everything is up to date
      print('📱 Performing full data refresh...');
      await loadContactsData(forceRefresh: true);

      // Update contact count after refresh
      if (_cachedDeviceContacts != null) {
        _lastKnownContactCount = _cachedDeviceContacts!.length;
        print(
            '📱 Updated contact count to $_lastKnownContactCount after manual refresh');
      }

      print('📱 ✅ Manual refresh completed successfully');
    } catch (e) {
      print('❌ Error during manual refresh: $e');
      // Fall back to regular force refresh
      await loadContactsData(forceRefresh: true);
    }
  }

  /// Quick contact check triggered manually (lightweight refresh)
  /// This method only checks for contact changes without full data refresh
  Future<void> quickContactCheck() async {
    print('📱 Quick contact check triggered...');

    try {
      await _checkForContactChanges();
      print('📱 ✅ Quick contact check completed');
    } catch (e) {
      print('❌ Error during quick contact check: $e');
    }
  }

  /// Preload device contacts cache in background for faster subsequent access
  void _preloadDeviceContactsCache() {
    // Run in background without awaiting to avoid blocking UI
    _getCachedDeviceContacts().then((contacts) {
      print('📱 Preloaded ${contacts.length} device contacts in background');
      // Initialize contact count for change detection
      _lastKnownContactCount = contacts.length;
    }).catchError((e) {
      print('⚠️ Background preload failed: $e');
    });
  }

  /// Start contact change detection to automatically detect new contacts
  void _startContactChangeDetection() {
    print('📱 Starting contact change detection...');

    _contactChangeDetectionTimer =
        Timer.periodic(_contactCheckInterval, (timer) {
      _checkForContactChanges();
    });
  }

  /// Check for changes in device contacts without full cache refresh
  Future<void> _checkForContactChanges() async {
    if (!_canUseService()) return;

    try {
      // Quick contact count check first
      final hasPermission = await ContactsService.requestContactsPermission();
      if (!hasPermission) return;

      // Get current contact count without full fetch
      final currentCount = await _getDeviceContactCount();

      if (_lastKnownContactCount != null &&
          currentCount != _lastKnownContactCount) {
        print(
            '📱 Contact count changed: ${_lastKnownContactCount} -> $currentCount');

        if (currentCount > _lastKnownContactCount!) {
          // New contacts added - perform incremental update
          print('📱 New contacts detected, performing incremental update...');
          await _performIncrementalContactUpdate();
        } else {
          // Contacts removed - full refresh needed
          print('📱 Contacts removed, performing full refresh...');
          await refreshDeviceContacts();
        }

        _lastKnownContactCount = currentCount;
      }
    } catch (e) {
      print('⚠️ Error checking for contact changes: $e');
    }
  }

  /// Get device contact count without fetching full contact data
  Future<int> _getDeviceContactCount() async {
    try {
      final deviceContacts = await ContactsService.getDeviceContacts();
      return deviceContacts.length;
    } catch (e) {
      print('⚠️ Error getting contact count: $e');
      return _lastKnownContactCount ?? 0;
    }
  }

  /// Perform incremental update to add only new contacts
  Future<void> _performIncrementalContactUpdate() async {
    try {
      // Get fresh device contacts
      final freshDeviceContacts = await ContactsService.getDeviceContacts();

      // If we have cached contacts, find the new ones
      if (_cachedDeviceContacts != null) {
        final existingIds = _cachedDeviceContacts!.map((c) => c.id).toSet();
        final newContacts = freshDeviceContacts
            .where((c) => !existingIds.contains(c.id))
            .toList();

        if (newContacts.isNotEmpty) {
          print(
              '📱 Found ${newContacts.length} new contacts, adding to cache...');

          // Add new contacts to cache
          _cachedDeviceContacts!.addAll(newContacts);

          // Update persistent cache
          await _saveToPersistentCache(_cachedDeviceContacts!, DateTime.now());

          // Rebuild contacts list to include new contacts
          await _rebuildContactsList();

          print('📱 ✅ Successfully added ${newContacts.length} new contacts');
        }
      } else {
        // No cache exists, do full refresh
        await refreshDeviceContacts();
      }
    } catch (e) {
      print('❌ Error performing incremental contact update: $e');
      // Fall back to full refresh
      await refreshDeviceContacts();
    }
  }

  /// Clear all caches (called during sign out)
  Future<void> clearAllCaches() async {
    print('📱 Clearing all DataStreamService caches');
    await clearDeviceContactsCache();
    _currentContacts.clear();

    // Safely emit empty contacts list
    if (!_contactsStreamController.isClosed) {
      _contactsStreamController.add([]);
    }
    _emitLoadingState(DataLoadingState.loading);
  }

  // Update emergency status for a specific contact and refresh UI
  Future<void> updateContactEmergencyStatus(
      String userId, EmergencyStatus? emergencyStatus) async {
    if (!_canUseService()) return;

    try {
      if (emergencyStatus == null) {
        print(
            '📱 Deleting emergency status for user $userId (emergency mode deactivated)');
      } else {
        print(
            '📱 Updating emergency status for user $userId: ${emergencyStatus.isActive}');
      }

      // Update the contact in the current list
      final updatedContacts = _currentContacts.map((contact) {
        if (contact.profile?.id == userId) {
          return contact.copyWith(emergencyStatus: emergencyStatus);
        }
        return contact;
      }).toList();

      // Update current contacts and emit to stream
      _currentContacts = updatedContacts;

      // Safely emit to stream
      if (!_contactsStreamController.isClosed) {
        _contactsStreamController.add(List.from(_currentContacts));
        if (emergencyStatus == null) {
          print(
              '📱 Emergency status deleted and UI refreshed for user $userId');
        } else {
          print(
              '📱 Emergency status updated and UI refreshed for user $userId');
        }
      }
    } catch (e) {
      print('❌ Failed to update emergency status for user $userId: $e');
    }
  }

  // Refresh emergency status for all contacts
  Future<void> refreshAllEmergencyStatuses() async {
    if (!_canUseService()) return;

    try {
      print('📱 Refreshing emergency status for all contacts...');

      // Update emergency status for all contacts with profiles
      final updatedContacts = _currentContacts.map((contact) {
        if (contact.profile != null) {
          final emergencyStatus = EmergencyModeService.getCachedEmergencyStatus(
              contact.profile!.id);
          return contact.copyWith(emergencyStatus: emergencyStatus);
        }
        return contact;
      }).toList();

      // Update current contacts and emit to stream
      _currentContacts = updatedContacts;

      // Safely emit to stream
      if (!_contactsStreamController.isClosed) {
        _contactsStreamController.add(List.from(_currentContacts));
        print('📱 Emergency status refreshed for all contacts');
      }
    } catch (e) {
      print('❌ Failed to refresh emergency statuses: $e');
    }
  }

  // Proactively fetch emergency status for all contacts when online
  Future<void> _proactivelyFetchEmergencyStatuses() async {
    if (!_canUseService()) return;

    final connectivityService = ConnectivityService();
    if (!connectivityService.isOnline) {
      print('📱 Device is offline - skipping proactive emergency status fetch');
      return;
    }

    try {
      print('📱 Proactively fetching emergency status for all contacts...');

      // Get all contacts with profiles
      final contactsWithProfiles =
          _currentContacts.where((contact) => contact.profile != null).toList();

      if (contactsWithProfiles.isEmpty) {
        print('📱 No contacts with profiles found for emergency status fetch');
        return;
      }

      print(
          '📱 Fetching emergency status for ${contactsWithProfiles.length} contacts...');

      // Fetch emergency status for each contact in parallel (but limit concurrency)
      final futures = <Future<void>>[];
      for (final contact in contactsWithProfiles) {
        final future = _fetchEmergencyStatusForContact(contact.profile!.id);
        futures.add(future);

        // Limit concurrent requests to avoid overwhelming the server
        if (futures.length >= 5) {
          await Future.wait(futures);
          futures.clear();
          // Small delay to be respectful to the server
          await Future.delayed(Duration(milliseconds: 100));
        }
      }

      // Wait for remaining requests
      if (futures.isNotEmpty) {
        await Future.wait(futures);
      }

      // Refresh UI with updated emergency statuses
      await refreshAllEmergencyStatuses();

      print('📱 Completed proactive emergency status fetch for all contacts');
    } catch (e) {
      print('❌ Failed to proactively fetch emergency statuses: $e');
    }
  }

  // Fetch emergency status for a single contact
  Future<void> _fetchEmergencyStatusForContact(String userId) async {
    try {
      // This will fetch from server and cache the result
      await EmergencyModeService.checkUserEmergencyStatus(userId);
    } catch (e) {
      print('⚠️ Failed to fetch emergency status for user $userId: $e');
      // Continue with other contacts even if one fails
    }
  }

  // Proactively fetch category assignments for all contacts when online
  Future<void> _proactivelyFetchCategoryAssignments() async {
    if (!_canUseService()) return;

    final connectivityService = ConnectivityService();
    if (!connectivityService.isOnline) {
      print(
          '📱 Device is offline - skipping proactive category assignments fetch');
      return;
    }

    try {
      print('📱 Proactively fetching category assignments for all contacts...');

      // Get current user profile
      final currentProfile = await _offlineService.getCurrentProfile();
      if (currentProfile == null) {
        print(
            '📱 No current user profile found for category assignments fetch');
        return;
      }

      // Get all contacts with profiles
      final contactsWithProfiles =
          _currentContacts.where((contact) => contact.profile != null).toList();

      if (contactsWithProfiles.isEmpty) {
        print(
            '📱 No contacts with profiles found for category assignments fetch');
        return;
      }

      print(
          '📱 Fetching category assignments for ${contactsWithProfiles.length} contacts...');

      // Use AssignedCategoriesService for proactive fetching of categories assigned BY current user TO contacts
      final contactPhones = contactsWithProfiles
          .map((contact) =>
              contact.profile?.phoneNumber ?? contact.primaryPhoneNumber)
          .where((phone) => phone.isNotEmpty)
          .toList();

      await AssignedCategoriesService.proactivelyFetchAssignedCategories(
        currentUserId: currentProfile.id,
        contactPhones: contactPhones,
      );

      print(
          '📱 Completed proactive category assignments fetch for all contacts');
    } catch (e) {
      print('❌ Failed to proactively fetch category assignments: $e');
    }
  }

  @override
  void dispose() {
    if (_isDisposed) return; // Prevent double disposal

    _isDisposed = true;
    _stopRealTimeSync();
    _contactChangeDetectionTimer?.cancel();
    _profilesSubscription?.cancel();
    _categoriesSubscription?.cancel();
    _assignmentsSubscription?.cancel();
    _emergencyStatusSubscription?.cancel();

    // Clear caches
    clearDeviceContactsCache();

    // Close stream controllers safely
    if (!_contactsStreamController.isClosed) {
      _contactsStreamController.close();
    }
    if (!_loadingStateController.isClosed) {
      _loadingStateController.close();
    }
    if (!_errorController.isClosed) {
      _errorController.close();
    }

    super.dispose();
  }

  // Safe method to check if service can be used
  bool _canUseService() {
    if (_isDisposed) {
      print('❌ DataStreamService was used after being disposed');
      return false;
    }
    return true;
  }

  // Method to safely restart the service if needed
  Future<void> restartService() async {
    print('🔄 Restarting DataStreamService...');

    // Clean up existing resources
    _stopRealTimeSync();
    _contactChangeDetectionTimer?.cancel();
    _profilesSubscription?.cancel();
    _categoriesSubscription?.cancel();
    _assignmentsSubscription?.cancel();

    // Reset state
    _isInitialized = false;
    _isDisposed = false;

    // Recreate stream controllers if they're closed
    if (_contactsStreamController.isClosed) {
      // Note: We can't recreate broadcast stream controllers
      // The app should be restarted in this case
      print('❌ Stream controllers are closed - app restart required');
      _emitError('App needs to be restarted. Please close and reopen the app.');
      return;
    }

    // Reinitialize
    await initialize();
    print('✅ DataStreamService restarted successfully');
  }
}

enum DataLoadingState {
  initial,
  loading,
  loaded,
  error,
}

class ContactWithProfile {
  final models.DeviceContact deviceContact;
  final models.Profile?
      profile; // Now optional - null if contact doesn't have app account
  final EmergencyStatus?
      emergencyStatus; // Cached emergency status for offline access

  ContactWithProfile({
    required this.deviceContact,
    this.profile,
    this.emergencyStatus,
  });

  String get displayName => deviceContact.displayName.isNotEmpty
      ? deviceContact.displayName
      : (profile?.fullName ?? deviceContact.phoneNumbers.first);

  /// Check if this contact has an app account
  bool get hasAppAccount => profile != null;

  /// Get the primary phone number for this contact
  String get primaryPhoneNumber => deviceContact.phoneNumbers.isNotEmpty
      ? deviceContact.phoneNumbers.first
      : '';

  /// Check if this contact is currently in emergency mode
  bool get isInEmergencyMode => emergencyStatus?.isActive == true;

  /// Create a copy with updated emergency status
  ContactWithProfile copyWith({
    models.DeviceContact? deviceContact,
    models.Profile? profile,
    EmergencyStatus? emergencyStatus,
  }) {
    return ContactWithProfile(
      deviceContact: deviceContact ?? this.deviceContact,
      profile: profile ?? this.profile,
      emergencyStatus: emergencyStatus ?? this.emergencyStatus,
    );
  }
}
