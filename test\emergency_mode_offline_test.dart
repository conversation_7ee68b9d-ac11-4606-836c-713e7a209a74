import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:contact_times/services/emergency_mode_service.dart';

void main() {
  group('Emergency Mode Offline Functionality Tests', () {
    setUp(() async {
      // Clear SharedPreferences before each test
      SharedPreferences.setMockInitialValues({});
    });

    test('Emergency status should be cached persistently', () async {
      // Create a test emergency status
      final testStatus = EmergencyStatus(
        isActive: true,
        startTime: DateTime.now().subtract(Duration(minutes: 30)),
        expiryTime: DateTime.now().add(Duration(hours: 2)),
        exceptionGroups: ['family', 'co-workers'],
        userName: 'Test User',
      );

      // Simulate caching the status
      final userId = 'test-user-123';
      EmergencyModeService.clearAllCache();
      
      // Add to cache (simulating online fetch)
      final cache = EmergencyModeService.getCachedEmergencyStatus(userId);
      expect(cache, isNull); // Should be null initially

      // Test that we can retrieve cached status
      // Note: In a real test, we would need to mock the cache properly
      // This is a basic structure for testing
    });

    test('Emergency status should persist across app restarts', () async {
      // Test that emergency status cache survives app restart
      // This would require mocking SharedPreferences with persistent data
      
      final testData = {
        'emergency_status_cache': '{"test-user-123":{"isActive":true,"startTime":"2024-01-01T10:00:00.000","expiryTime":"2024-01-01T12:00:00.000","exceptionGroups":["family"],"userName":"Test User","cachedAt":"2024-01-01T10:00:00.000"}}',
        'emergency_status_cache_time': '2024-01-01T10:00:00.000'
      };
      
      SharedPreferences.setMockInitialValues(testData);
      
      // Initialize service and check if data is loaded
      final service = EmergencyModeService();
      await service.initialize();
      
      // Check if cached data is available
      final cachedStatus = EmergencyModeService.getCachedEmergencyStatus('test-user-123');
      expect(cachedStatus, isNotNull);
      expect(cachedStatus?.isActive, isTrue);
      expect(cachedStatus?.userName, equals('Test User'));
      expect(cachedStatus?.exceptionGroups, contains('family'));
    });

    test('Emergency status should fallback to cache when offline', () async {
      // Test offline behavior
      // This would require mocking connectivity service
      
      // Simulate having cached data
      final testData = {
        'emergency_status_cache': '{"test-user-456":{"isActive":true,"startTime":"2024-01-01T10:00:00.000","expiryTime":"2024-01-01T12:00:00.000","exceptionGroups":["co-workers"],"userName":"Offline User","cachedAt":"2024-01-01T10:00:00.000"}}',
      };
      
      SharedPreferences.setMockInitialValues(testData);
      
      final service = EmergencyModeService();
      await service.initialize();
      
      // Test that cached data is returned when offline
      final cachedStatus = EmergencyModeService.getCachedEmergencyStatus('test-user-456');
      expect(cachedStatus, isNotNull);
      expect(cachedStatus?.userName, equals('Offline User'));
    });

    test('Emergency status cache should be cleared on sign out', () async {
      // Test cache cleanup
      final testData = {
        'emergency_status_cache': '{"test-user-789":{"isActive":true,"startTime":"2024-01-01T10:00:00.000","expiryTime":"2024-01-01T12:00:00.000","exceptionGroups":[],"userName":"Clear Test","cachedAt":"2024-01-01T10:00:00.000"}}',
        'emergency_status_cache_time': '2024-01-01T10:00:00.000'
      };
      
      SharedPreferences.setMockInitialValues(testData);
      
      final service = EmergencyModeService();
      await service.initialize();
      
      // Verify data exists
      final prefs = await SharedPreferences.getInstance();
      expect(prefs.getString('emergency_status_cache'), isNotNull);
      
      // Clear all emergency data (simulate sign out)
      await service.clearAllEmergencyData();
      
      // Verify data is cleared
      expect(prefs.getString('emergency_status_cache'), isNull);
      expect(prefs.getString('emergency_status_cache_time'), isNull);
    });

    test('Emergency status should handle expired cache entries', () async {
      // Test cache expiration logic
      final expiredTime = DateTime.now().subtract(Duration(minutes: 5));
      
      final testData = {
        'emergency_status_cache': '{"expired-user":{"isActive":true,"startTime":"2024-01-01T10:00:00.000","expiryTime":"2024-01-01T12:00:00.000","exceptionGroups":[],"userName":"Expired User","cachedAt":"${expiredTime.toIso8601String()}"}}',
      };
      
      SharedPreferences.setMockInitialValues(testData);
      
      final service = EmergencyModeService();
      await service.initialize();
      
      // Test that expired cache returns null
      final cachedStatus = EmergencyModeService.getCachedEmergencyStatus('expired-user');
      // Note: This test would need proper cache expiration logic implementation
      // Currently the cache expiration is set to 30 seconds in EmergencyStatusCache.isExpired
    });

    test('Emergency status updates should trigger UI refresh', () async {
      // Test stream-based updates
      final service = EmergencyModeService();
      await service.initialize();
      
      bool updateReceived = false;
      String? updatedUserId;
      EmergencyStatus? updatedStatus;
      
      // Listen to emergency status updates
      final subscription = EmergencyModeService.emergencyStatusUpdateStream.listen((updates) {
        updateReceived = true;
        if (updates.isNotEmpty) {
          final entry = updates.entries.first;
          updatedUserId = entry.key;
          updatedStatus = entry.value;
        }
      });
      
      // Simulate an emergency status update
      final testStatus = EmergencyStatus(
        isActive: true,
        startTime: DateTime.now(),
        expiryTime: DateTime.now().add(Duration(hours: 1)),
        exceptionGroups: ['emergency'],
        userName: 'Stream Test User',
      );
      
      // This would trigger the stream update in real implementation
      // EmergencyModeService._emitEmergencyStatusUpdate('stream-test-user', testStatus);
      
      // Clean up
      await subscription.cancel();
      
      // Note: This test structure shows how to test stream updates
      // The actual implementation would need proper stream testing
    });
  });
}
