import 'package:flutter_test/flutter_test.dart';
import 'package:contact_times/services/contacts_service.dart';

void main() {
  group('Enhanced Category Assignment Logic Tests', () {
    test('should generate phone variations correctly', () {
      // Test Yemen number variations
      final yemenVariations =
          ContactsService.generatePhoneVariations('775515722');
      expect(yemenVariations.contains('775515722'), true);
      expect(yemenVariations.contains('+775515722'), true);

      // Test number with country code
      final withCountryCode =
          ContactsService.generatePhoneVariations('+967775515722');
      expect(withCountryCode.contains('967775515722'), true);
      expect(withCountryCode.contains('775515722'), true);
      expect(withCountryCode.contains('+967775515722'), true);
    });

    test('should match phone numbers with different formats', () {
      // Test basic matching
      expect(ContactsService.doPhoneNumbersMatch('775515722', '+967775515722'),
          true);
      expect(ContactsService.doPhoneNumbersMatch('967775515722', '775515722'),
          true);
      expect(
          ContactsService.doPhoneNumbersMatch('+967775515722', '967775515722'),
          true);

      // Test with formatting characters
      expect(
          ContactsService.doPhoneNumbersMatch(
              '+967-775-515-722', '967775515722'),
          true);
      expect(
          ContactsService.doPhoneNumbersMatch(
              '(967) 775-515-722', '+967775515722'),
          true);
    });

    test('should handle multiple phone number scenarios', () {
      // Simulate userB having multiple phone numbers
      final userBPhones = [
        '+967775515722', // International format
        '775515722', // Local format
        '967775515722', // Country code without +
        '0775515722', // With leading zero
      ];

      // Simulate existing categorized phones for userA
      final existingPhones = {
        '775515722', // Previously categorized local format
        '123456789', // Different contact
      };

      // Test NEW LOGIC: Find ALL userB related phones
      final userBRelatedPhones = <String>[];
      final allUserBPhones = <String>[];

      // Clean all userB phone numbers
      for (final userBPhone in userBPhones) {
        final cleanedUserBPhone = userBPhone.replaceAll(RegExp(r'[^\d]'), '');
        if (cleanedUserBPhone.isNotEmpty) {
          allUserBPhones.add(cleanedUserBPhone);
        }
      }

      // Find ALL existing phone numbers that belong to userB (any variation match)
      for (final existingPhone in existingPhones) {
        for (final userBPhone in allUserBPhones) {
          if (ContactsService.doPhoneNumbersMatch(existingPhone, userBPhone)) {
            userBRelatedPhones.add(existingPhone);
            break; // Found match for this existing phone, move to next
          }
        }
      }

      // Should find the Yemen number in existing phones
      expect(userBRelatedPhones.isNotEmpty, true);
      expect(userBRelatedPhones.contains('775515722'), true);

      // All userB phones should be processed (cleaned)
      expect(allUserBPhones.length, greaterThan(0));
      expect(allUserBPhones.contains('967775515722'), true);
      expect(allUserBPhones.contains('775515722'), true);
    });

    test('should handle scenario when userA adds new phone for existing userB',
        () {
      // Simulate userB having existing categorized phones
      final existingUserBPhones = [
        '775515722',
        '778576141'
      ]; // Two phones already categorized

      // Simulate userA adding a new phone for userB
      final allUserBPhones = [
        '775515722', // Existing phone 1
        '778576141', // Existing phone 2
        '736562925', // NEW phone added by userA
      ];

      // Simulate existing categorized phones for userA
      final existingPhones = {
        '775515722', // Previously categorized
        '778576141', // Previously categorized
        '123456789', // Different contact
      };

      // Test NEW LOGIC: Find ALL userB related phones
      final userBRelatedPhones = <String>[];

      // Find ALL existing phone numbers that belong to userB
      for (final existingPhone in existingPhones) {
        for (final userBPhone in allUserBPhones) {
          if (ContactsService.doPhoneNumbersMatch(existingPhone, userBPhone)) {
            userBRelatedPhones.add(existingPhone);
            break;
          }
        }
      }

      // Should find both existing userB phones
      expect(userBRelatedPhones.length, equals(2));
      expect(userBRelatedPhones.contains('775515722'), true);
      expect(userBRelatedPhones.contains('778576141'), true);

      // Should identify the new phone that needs to be inserted
      final newPhones = <String>[];
      for (final userBPhone in allUserBPhones) {
        bool alreadyExists = false;
        for (final existingPhone in userBRelatedPhones) {
          if (ContactsService.doPhoneNumbersMatch(userBPhone, existingPhone)) {
            alreadyExists = true;
            break;
          }
        }
        if (!alreadyExists) {
          newPhones.add(userBPhone);
        }
      }

      // Should find the new phone
      expect(newPhones.length, equals(1));
      expect(newPhones.contains('736562925'), true);
    });

    test('should handle orphaned userB phones with different categories', () {
      // Simulate the exact scenario from the database screenshot
      final userBPhones = [
        '778576141', // Existing phone 1 with old category
        '736562925', // Existing phone 2 with old category
        '967775515722', // NEW phone being added with new category
      ];

      // Simulate ALL existing categorized phones for userA
      final allExistingPhones = {
        '778576141': 'old-category-id', // UserB phone with old category
        '736562925': 'old-category-id', // UserB phone with old category
        '123456789': 'other-category-id', // Different contact
        '987654321': 'another-category-id', // Different contact
      };

      final newCategoryId = 'new-category-id';

      // Test the orphaned phone detection logic
      final orphanedPhones = <String>[];

      for (final entry in allExistingPhones.entries) {
        final existingPhone = entry.key;
        final existingCategoryId = entry.value;

        // Skip if this phone already has the correct category
        if (existingCategoryId == newCategoryId) continue;

        // Check if this existing phone matches any of userB's phones
        bool isUserBPhone = false;
        for (final userBPhone in userBPhones) {
          if (ContactsService.doPhoneNumbersMatch(existingPhone, userBPhone)) {
            isUserBPhone = true;
            break;
          }
        }

        if (isUserBPhone) {
          orphanedPhones.add(existingPhone);
        }
      }

      // Should find both existing userB phones as orphaned (needing category update)
      expect(orphanedPhones.length, equals(2));
      expect(orphanedPhones.contains('778576141'), true);
      expect(orphanedPhones.contains('736562925'), true);

      // Should NOT include phones from other contacts
      expect(orphanedPhones.contains('123456789'), false);
      expect(orphanedPhones.contains('987654321'), false);
    });

    test('should handle no existing matches scenario', () {
      // Simulate userB having phone numbers not previously categorized
      final userBPhones = [
        '+1234567890', // US number
        '1234567890', // Same US number without +
      ];

      // Simulate existing categorized phones for userA (different numbers)
      final existingPhones = {
        '775515722', // Yemen number
        '987654321', // Different number
      };

      // Test matching logic
      final matchedPhones = <String>[];
      final newPhones = <String>[];

      for (final userBPhone in userBPhones) {
        final cleanedUserBPhone = userBPhone.replaceAll(RegExp(r'[^\d]'), '');
        if (cleanedUserBPhone.isEmpty) continue;

        bool foundMatch = false;

        // Check for exact matches first
        if (existingPhones.contains(cleanedUserBPhone)) {
          matchedPhones.add(cleanedUserBPhone);
          foundMatch = true;
        } else {
          // Check for phone number variations match
          for (final existingPhone in existingPhones) {
            if (ContactsService.doPhoneNumbersMatch(
                cleanedUserBPhone, existingPhone)) {
              matchedPhones.add(existingPhone);
              foundMatch = true;
              break;
            }
          }
        }

        if (!foundMatch) {
          newPhones.add(cleanedUserBPhone);
        }
      }

      // Should not find any matches
      expect(matchedPhones.isEmpty, true);

      // All phones should be new
      expect(newPhones.length, greaterThan(0));
      expect(newPhones.contains('1234567890'), true);
    });

    test('should clean phone numbers correctly', () {
      final testCases = [
        ['+967-775-515-722', '967775515722'],
        ['(967) 775-515-722', '967775515722'],
        ['+967 775 515 722', '967775515722'],
        ['967.775.515.722', '967775515722'],
        ['775515722', '775515722'],
      ];

      for (final testCase in testCases) {
        final input = testCase[0];
        final expected = testCase[1];
        final cleaned = input.replaceAll(RegExp(r'[^\d]'), '');
        expect(cleaned, expected, reason: 'Failed to clean $input');
      }
    });
  });
}
