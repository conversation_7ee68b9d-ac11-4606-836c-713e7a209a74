package com.example.contact_times

import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.example.contact_times/phone"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "launchDialer" -> {
                    val phoneNumber = call.argument<String>("phoneNumber")
                    if (phoneNumber != null) {
                        launchDialer(phoneNumber, result)
                    } else {
                        result.error("INVALID_ARGUMENT", "Phone number is required", null)
                    }
                }
                "checkDialerApps" -> {
                    checkDialerApps(result)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun launchDialer(phoneNumber: String, result: MethodChannel.Result) {
        try {
            // Create the primary intent
            val dialIntent = Intent(Intent.ACTION_DIAL).apply {
                data = Uri.parse("tel:$phoneNumber")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }

            // Check if we can resolve the intent
            val resolveInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                packageManager.resolveActivity(dialIntent, PackageManager.ResolveInfoFlags.of(PackageManager.MATCH_DEFAULT_ONLY.toLong()))
            } else {
                @Suppress("DEPRECATION")
                packageManager.resolveActivity(dialIntent, PackageManager.MATCH_DEFAULT_ONLY)
            }

            if (resolveInfo != null) {
                startActivity(dialIntent)
                result.success("Phone dialer launched successfully")
                return
            }

            // If ACTION_DIAL failed, try ACTION_VIEW
            val viewIntent = Intent(Intent.ACTION_VIEW).apply {
                data = Uri.parse("tel:$phoneNumber")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }

            val viewResolveInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                packageManager.resolveActivity(viewIntent, PackageManager.ResolveInfoFlags.of(PackageManager.MATCH_DEFAULT_ONLY.toLong()))
            } else {
                @Suppress("DEPRECATION")
                packageManager.resolveActivity(viewIntent, PackageManager.MATCH_DEFAULT_ONLY)
            }

            if (viewResolveInfo != null) {
                startActivity(viewIntent)
                result.success("Phone dialer launched successfully")
                return
            }

            // Last resort: try to launch any app that can handle tel: URIs
            try {
                val chooserIntent = Intent.createChooser(dialIntent, "Choose Phone App")
                chooserIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                startActivity(chooserIntent)
                result.success("Phone dialer launched successfully")
            } catch (e: Exception) {
                // Final fallback: try to launch common dialer apps by package name
                val commonDialerPackages = listOf(
                    "com.android.dialer",
                    "com.google.android.dialer",
                    "com.samsung.android.dialer",
                    "com.htc.android.dialer",
                    "com.android.contacts"
                )

                var launched = false
                for (packageName in commonDialerPackages) {
                    try {
                        val launchIntent = packageManager.getLaunchIntentForPackage(packageName)
                        if (launchIntent != null) {
                            launchIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                            startActivity(launchIntent)
                            launched = true
                            break
                        }
                    } catch (ex: Exception) {
                        continue
                    }
                }

                if (launched) {
                    result.success("Phone dialer launched successfully")
                } else {
                    result.error("NO_DIALER", "No phone dialer app found on device", null)
                }
            }

        } catch (e: Exception) {
            result.error("LAUNCH_ERROR", "Failed to launch phone dialer: ${e.message}", null)
        }
    }

    private fun checkDialerApps(result: MethodChannel.Result) {
        try {
            val dialIntent = Intent(Intent.ACTION_DIAL).apply {
                data = Uri.parse("tel:123456789")
            }

            val activities = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                packageManager.queryIntentActivities(dialIntent, PackageManager.ResolveInfoFlags.of(0))
            } else {
                @Suppress("DEPRECATION")
                packageManager.queryIntentActivities(dialIntent, 0)
            }

            val appNames = activities.map {
                "${it.activityInfo.packageName} - ${it.loadLabel(packageManager)}"
            }

            result.success(mapOf(
                "count" to activities.size,
                "apps" to appNames
            ))
        } catch (e: Exception) {
            result.error("CHECK_ERROR", "Failed to check dialer apps: ${e.message}", null)
        }
    }
}
