// Basic Flutter widget test for Contact Times app

import 'package:flutter_test/flutter_test.dart';

import 'package:contact_times/main.dart';

void main() {
  testWidgets('App loads correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ContactTimesApp());

    // Verify that the app loads with sign in screen
    expect(find.text('Contact Times'), findsOneWidget);
    expect(find.text('Sign In'), findsOneWidget);
  });
}
