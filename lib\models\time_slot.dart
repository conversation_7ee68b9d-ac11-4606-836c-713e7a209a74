import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';

class TimeSlot {
  final String id;
  final String categoryId;
  final int dayOfWeek; // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
  final CustomTimeOfDay startTime;
  final CustomTimeOfDay endTime;
  final DateTime createdAt;

  TimeSlot({
    required this.id,
    required this.categoryId,
    required this.dayOfWeek,
    required this.startTime,
    required this.endTime,
    required this.createdAt,
  });

  factory TimeSlot.fromJson(Map<String, dynamic> json) {
    return TimeSlot(
      id: json['id'] as String,
      categoryId: json['category_id'] as String,
      dayOfWeek: json['day_of_week'] as int,
      startTime: CustomTimeOfDay.fromString(json['start_time'] as String),
      endTime: CustomTimeOfDay.fromString(json['end_time'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category_id': categoryId,
      'day_of_week': dayOfWeek,
      'start_time': startTime.toString(),
      'end_time': endTime.toString(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  TimeSlot copyWith({
    String? id,
    String? categoryId,
    int? dayOfWeek,
    CustomTimeOfDay? startTime,
    CustomTimeOfDay? endTime,
    DateTime? createdAt,
  }) {
    return TimeSlot(
      id: id ?? this.id,
      categoryId: categoryId ?? this.categoryId,
      dayOfWeek: dayOfWeek ?? this.dayOfWeek,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  String get dayName {
    const days = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday'
    ];
    return days[dayOfWeek];
  }

  String getDayName(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    if (l10n == null) return dayName;

    switch (dayOfWeek) {
      case 0:
        return l10n.sunday;
      case 1:
        return l10n.monday;
      case 2:
        return l10n.tuesday;
      case 3:
        return l10n.wednesday;
      case 4:
        return l10n.thursday;
      case 5:
        return l10n.friday;
      case 6:
        return l10n.saturday;
      default:
        return dayName;
    }
  }

  String get timeRange {
    return '${startTime.format()} - ${endTime.format()}';
  }

  bool isCurrentTimeInSlot() {
    final now = DateTime.now();
    // Convert DateTime.weekday (1=Monday, 7=Sunday) to our format (0=Sunday, 6=Saturday)
    final currentDay = now.weekday == 7 ? 0 : now.weekday;
    final currentTime = CustomTimeOfDay(hour: now.hour, minute: now.minute);

    if (currentDay != dayOfWeek) return false;

    return currentTime.isAfterOrEqual(startTime) &&
        currentTime.isBeforeOrEqual(endTime);
  }

  @override
  String toString() {
    return 'TimeSlot(dayOfWeek: $dayName, timeRange: $timeRange)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TimeSlot && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  /// Check if this time slot overlaps with another time slot
  bool overlapsWith(TimeSlot other) {
    // Only check overlap if they're on the same day
    if (dayOfWeek != other.dayOfWeek) return false;

    // Check if time ranges overlap
    // Two ranges [a,b] and [c,d] overlap if: a <= d && c <= b
    return startTime.isBeforeOrEqual(other.endTime) &&
        other.startTime.isBeforeOrEqual(endTime);
  }

  /// Check if this time slot is adjacent to another (touching but not overlapping)
  bool isAdjacentTo(TimeSlot other) {
    // Only check adjacency if they're on the same day
    if (dayOfWeek != other.dayOfWeek) return false;

    // Adjacent if one ends exactly when the other starts
    return (endTime.compareTo(other.startTime) == 0) ||
        (other.endTime.compareTo(startTime) == 0);
  }

  /// Merge this time slot with another overlapping or adjacent time slot
  TimeSlot mergeWith(TimeSlot other) {
    if (dayOfWeek != other.dayOfWeek) {
      throw ArgumentError('Cannot merge time slots from different days');
    }

    // Find the earliest start time and latest end time
    final earliestStart =
        startTime.isBefore(other.startTime) ? startTime : other.startTime;
    final latestEnd = endTime.isAfter(other.endTime) ? endTime : other.endTime;

    return TimeSlot(
      id: id, // Keep the ID of the first slot
      categoryId: categoryId,
      dayOfWeek: dayOfWeek,
      startTime: earliestStart,
      endTime: latestEnd,
      createdAt: createdAt,
    );
  }

  /// Static method to merge overlapping time slots in a list
  static List<TimeSlot> mergeOverlappingSlots(List<TimeSlot> timeSlots) {
    if (timeSlots.isEmpty) return [];

    // Group time slots by day
    final Map<int, List<TimeSlot>> slotsByDay = {};
    for (final slot in timeSlots) {
      slotsByDay.putIfAbsent(slot.dayOfWeek, () => []).add(slot);
    }

    final List<TimeSlot> mergedSlots = [];

    // Process each day separately
    for (final daySlots in slotsByDay.values) {
      // Sort slots by start time
      daySlots.sort((a, b) => a.startTime.compareTo(b.startTime));

      final List<TimeSlot> dayMergedSlots = [];
      TimeSlot? currentSlot;

      for (final slot in daySlots) {
        if (currentSlot == null) {
          currentSlot = slot;
        } else if (currentSlot.overlapsWith(slot) ||
            currentSlot.isAdjacentTo(slot)) {
          // Merge with current slot
          currentSlot = currentSlot.mergeWith(slot);
        } else {
          // No overlap, add current slot to result and start new one
          dayMergedSlots.add(currentSlot);
          currentSlot = slot;
        }
      }

      // Add the last slot
      if (currentSlot != null) {
        dayMergedSlots.add(currentSlot);
      }

      mergedSlots.addAll(dayMergedSlots);
    }

    return mergedSlots;
  }
}

class CustomTimeOfDay {
  final int hour;
  final int minute;

  const CustomTimeOfDay({required this.hour, required this.minute});

  factory CustomTimeOfDay.fromString(String timeString) {
    // Parse time string in format "HH:MM:SS" or "HH:MM"
    final parts = timeString.split(':');
    return CustomTimeOfDay(
      hour: int.parse(parts[0]),
      minute: int.parse(parts[1]),
    );
  }

  factory CustomTimeOfDay.now() {
    final now = DateTime.now();
    return CustomTimeOfDay(hour: now.hour, minute: now.minute);
  }

  String format() {
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    final displayMinute = minute.toString().padLeft(2, '0');
    return '$displayHour:$displayMinute $period';
  }

  @override
  String toString() {
    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}:00';
  }

  bool isAfterOrEqual(CustomTimeOfDay other) {
    if (hour > other.hour) return true;
    if (hour == other.hour && minute >= other.minute) return true;
    return false;
  }

  bool isBeforeOrEqual(CustomTimeOfDay other) {
    if (hour < other.hour) return true;
    if (hour == other.hour && minute <= other.minute) return true;
    return false;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CustomTimeOfDay &&
        other.hour == hour &&
        other.minute == minute;
  }

  @override
  int get hashCode => hour.hashCode ^ minute.hashCode;

  /// Compare two times and return -1, 0, or 1
  int compareTo(CustomTimeOfDay other) {
    if (hour < other.hour) return -1;
    if (hour > other.hour) return 1;
    if (minute < other.minute) return -1;
    if (minute > other.minute) return 1;
    return 0;
  }

  /// Check if this time is before another time
  bool isBefore(CustomTimeOfDay other) {
    return compareTo(other) < 0;
  }

  /// Check if this time is after another time
  bool isAfter(CustomTimeOfDay other) {
    return compareTo(other) > 0;
  }
}
