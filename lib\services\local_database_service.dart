import 'dart:async';
import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/category.dart';
import '../models/profile.dart';
import '../models/time_slot.dart';
import '../models/notification_preference.dart';

class LocalDatabaseService {
  static Database? _database;
  static const String _databaseName = 'contact_times_offline.db';
  static const int _databaseVersion = 3;

  // Table names
  static const String _profilesTable = 'profiles';
  static const String _categoriesTable = 'categories';
  static const String _timeSlotsTable = 'time_slots';
  static const String _userContactsTable = 'user_contacts';
  static const String _notificationPreferencesTable = 'notification_preferences';
  static const String _syncQueueTable = 'sync_queue';
  static const String _notificationsTable = 'local_notifications';
  static const String _syncMetadataTable = 'sync_metadata';

  static Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  static Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), _databaseName);
    
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _createTables,
      onUpgrade: _onUpgrade,
    );
  }

  static Future<void> _createTables(Database db, int version) async {
    // Profiles table
    await db.execute('''
      CREATE TABLE $_profilesTable (
        id TEXT PRIMARY KEY,
        display_name TEXT,
        username TEXT,
        phone_number TEXT NOT NULL,
        avatar_url TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        last_synced TEXT
      )
    ''');

    // Categories table
    await db.execute('''
      CREATE TABLE $_categoriesTable (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        type INTEGER NOT NULL,
        note TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        last_synced TEXT
      )
    ''');

    // Time slots table
    await db.execute('''
      CREATE TABLE $_timeSlotsTable (
        id TEXT PRIMARY KEY,
        category_id TEXT NOT NULL,
        day_of_week INTEGER NOT NULL,
        start_time TEXT NOT NULL,
        end_time TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        last_synced TEXT,
        FOREIGN KEY (category_id) REFERENCES $_categoriesTable (id) ON DELETE CASCADE
      )
    ''');

    // User contacts table (assignments)
    await db.execute('''
      CREATE TABLE $_userContactsTable (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        categorized_contact_phone TEXT NOT NULL,
        assigned_category_id TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        last_synced TEXT,
        FOREIGN KEY (assigned_category_id) REFERENCES $_categoriesTable (id) ON DELETE CASCADE
      )
    ''');

    // Notification preferences table
    await db.execute('''
      CREATE TABLE $_notificationPreferencesTable (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        contact_user_id TEXT NOT NULL,
        time_slot_id TEXT NOT NULL,
        is_enabled INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        last_synced TEXT,
        UNIQUE(user_id, contact_user_id, time_slot_id),
        FOREIGN KEY (time_slot_id) REFERENCES $_timeSlotsTable (id) ON DELETE CASCADE
      )
    ''');

    // Sync queue table for offline operations
    await db.execute('''
      CREATE TABLE $_syncQueueTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        operation_type TEXT NOT NULL,
        table_name TEXT NOT NULL,
        record_id TEXT NOT NULL,
        data TEXT NOT NULL,
        created_at TEXT NOT NULL,
        retry_count INTEGER DEFAULT 0
      )
    ''');

    // Local notifications table
    await db.execute('''
      CREATE TABLE $_notificationsTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        contact_phone TEXT NOT NULL,
        contact_name TEXT NOT NULL,
        time_slot_id TEXT NOT NULL,
        scheduled_time TEXT NOT NULL,
        created_at TEXT NOT NULL,
        synced INTEGER DEFAULT 0
      )
    ''');

    // Sync metadata table for tracking sync timestamps
    await db.execute('''
      CREATE TABLE $_syncMetadataTable (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_profiles_phone ON $_profilesTable (phone_number)');
    await db.execute('CREATE INDEX idx_categories_user ON $_categoriesTable (user_id)');
    await db.execute('CREATE INDEX idx_time_slots_category ON $_timeSlotsTable (category_id)');
    await db.execute('CREATE INDEX idx_user_contacts_user ON $_userContactsTable (user_id)');
    await db.execute('CREATE INDEX idx_user_contacts_phone ON $_userContactsTable (categorized_contact_phone)');
    await db.execute('CREATE INDEX idx_sync_queue_type ON $_syncQueueTable (operation_type)');
  }

  static Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < 2) {
      // Add sync_metadata table in version 2
      await db.execute('''
        CREATE TABLE $_syncMetadataTable (
          key TEXT PRIMARY KEY,
          value TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      ''');

      // Add notification_preferences table in version 2
      await db.execute('''
        CREATE TABLE $_notificationPreferencesTable (
          id TEXT PRIMARY KEY,
          user_id TEXT NOT NULL,
          contact_user_id TEXT NOT NULL,
          time_slot_id TEXT NOT NULL,
          is_enabled INTEGER NOT NULL DEFAULT 1,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          last_synced TEXT,
          UNIQUE(user_id, contact_user_id, time_slot_id),
          FOREIGN KEY (time_slot_id) REFERENCES $_timeSlotsTable (id) ON DELETE CASCADE
        )
      ''');
    }

    if (oldVersion < 3) {
      // Add missing columns to profiles table in version 3
      try {
        await db.execute('ALTER TABLE $_profilesTable ADD COLUMN username TEXT');
      } catch (e) {
        // Column might already exist, ignore error
      }

      try {
        await db.execute('ALTER TABLE $_profilesTable ADD COLUMN avatar_url TEXT');
      } catch (e) {
        // Column might already exist, ignore error
      }

      // Also make display_name nullable since it might be null for some profiles
      // Note: SQLite doesn't support modifying column constraints, so we'll handle this in the code
    }
  }

  // Profile operations
  static Future<void> insertProfile(Profile profile) async {
    final db = await database;
    print('💾 Inserting profile to database: ${profile.id}, avatar: ${profile.avatarUrl}');
    await db.insert(
      _profilesTable,
      {
        'id': profile.id,
        'display_name': profile.fullName,
        'username': profile.username,
        'phone_number': profile.phoneNumber,
        'avatar_url': profile.avatarUrl,
        'created_at': profile.createdAt.toIso8601String(),
        'updated_at': profile.updatedAt.toIso8601String(),
        'last_synced': DateTime.now().toIso8601String(),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    print('💾 Profile inserted successfully');
  }

  static Future<List<Profile>> getAllProfiles() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(_profilesTable);

    return List.generate(maps.length, (i) {
      return Profile(
        id: maps[i]['id'],
        fullName: maps[i]['display_name'],
        username: maps[i]['username'],
        phoneNumber: maps[i]['phone_number'],
        avatarUrl: maps[i]['avatar_url'],
        createdAt: DateTime.parse(maps[i]['created_at']),
        updatedAt: DateTime.parse(maps[i]['updated_at']),
      );
    });
  }

  static Future<Profile?> getProfileById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _profilesTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      print('💾 Retrieved profile from database: ${maps[0]['id']}, avatar: ${maps[0]['avatar_url']}');
      return Profile(
        id: maps[0]['id'],
        fullName: maps[0]['display_name'],
        username: maps[0]['username'],
        phoneNumber: maps[0]['phone_number'],
        avatarUrl: maps[0]['avatar_url'],
        createdAt: DateTime.parse(maps[0]['created_at']),
        updatedAt: DateTime.parse(maps[0]['updated_at']),
      );
    }
    print('💾 No profile found in database for ID: $id');
    return null;
  }

  static Future<Profile?> getProfileByPhone(String phoneNumber) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _profilesTable,
      where: 'phone_number = ?',
      whereArgs: [phoneNumber],
    );
    
    if (maps.isNotEmpty) {
      return Profile(
        id: maps[0]['id'],
        fullName: maps[0]['display_name'],
        phoneNumber: maps[0]['phone_number'],
        createdAt: DateTime.parse(maps[0]['created_at']),
        updatedAt: DateTime.parse(maps[0]['updated_at']),
      );
    }
    return null;
  }

  // Category operations
  static Future<void> insertCategory(Category category) async {
    try {
      print('📱 Inserting category: ${category.type.name} (ID: ${category.id}) with ${category.timeSlots.length} time slots');
      final db = await database;

      // Check if category already exists
      final existing = await db.query(
        _categoriesTable,
        where: 'id = ?',
        whereArgs: [category.id],
      );

      if (existing.isNotEmpty) {
        print('📱 Category already exists, updating instead of inserting');
        await db.update(
          _categoriesTable,
          {
            'user_id': category.userId,
            'type': category.type.index,
            'note': category.note,
            'created_at': category.createdAt.toIso8601String(),
            'updated_at': category.updatedAt.toIso8601String(),
            'last_synced': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [category.id],
        );
      } else {
        await db.insert(
          _categoriesTable,
          {
            'id': category.id,
            'user_id': category.userId,
            'type': category.type.index,
            'note': category.note,
            'created_at': category.createdAt.toIso8601String(),
            'updated_at': category.updatedAt.toIso8601String(),
            'last_synced': DateTime.now().toIso8601String(),
          },
        );
      }

      // Clear existing time slots for this category to avoid duplicates
      await db.delete(
        _timeSlotsTable,
        where: 'category_id = ?',
        whereArgs: [category.id],
      );

      // Insert time slots
      print('📱 Inserting ${category.timeSlots.length} time slots for category ${category.id}');
      for (final timeSlot in category.timeSlots) {
        print('📱   Inserting time slot: ${timeSlot.dayName} ${timeSlot.timeRange} (ID: ${timeSlot.id}, CategoryID: ${timeSlot.categoryId})');
        await insertTimeSlot(timeSlot);
        print('📱   ✅ Inserted time slot: ${timeSlot.dayName} ${timeSlot.timeRange}');
      }

      // Verify the category was inserted
      final verification = await getCategoryById(category.id);
      if (verification != null) {
        print('📱 ✅ Category verified in database with ${verification.timeSlots.length} time slots');
      } else {
        print('📱 ❌ Category verification failed - not found in database');
      }
    } catch (e) {
      print('📱 ❌ Error inserting category: $e');
      rethrow;
    }
  }

  static Future<List<Category>> getCategoriesByUserId(String userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _categoriesTable,
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'created_at ASC', // Ensure consistent ordering
    );

    print('📱 Found ${maps.length} categories for user $userId');

    List<Category> categories = [];
    Set<String> seenCategoryIds = {}; // Track seen category IDs to prevent duplicates

    for (final map in maps) {
      final categoryId = map['id'] as String;

      // Skip if we've already processed this category ID
      if (seenCategoryIds.contains(categoryId)) {
        print('📱 Skipping duplicate category: $categoryId');
        continue;
      }
      seenCategoryIds.add(categoryId);

      final timeSlots = await getTimeSlotsByCategoryId(categoryId);
      categories.add(Category(
        id: categoryId,
        userId: map['user_id'],
        type: CategoryType.values[map['type']],
        note: map['note'],
        timeSlots: timeSlots,
        createdAt: DateTime.parse(map['created_at']),
        updatedAt: DateTime.parse(map['updated_at']),
      ));
    }

    print('📱 Returning ${categories.length} unique categories');
    return categories;
  }

  static Future<Category?> getCategoryById(String id) async {
    print('🔍 getCategoryById called with ID: $id');
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _categoriesTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    print('🔍 Found ${maps.length} categories with ID: $id');
    if (maps.isNotEmpty) {
      print('🔍 Category found: ${maps[0]['note']}');
      final timeSlots = await getTimeSlotsByCategoryId(id);
      print('🔍 Loaded ${timeSlots.length} time slots for category $id');
      for (final slot in timeSlots) {
        print('🔍   - ${slot.dayName}: ${slot.timeRange}');
      }
      return Category(
        id: maps[0]['id'],
        userId: maps[0]['user_id'],
        type: CategoryType.values[maps[0]['type']],
        note: maps[0]['note'],
        timeSlots: timeSlots,
        createdAt: DateTime.parse(maps[0]['created_at']),
        updatedAt: DateTime.parse(maps[0]['updated_at']),
      );
    }
    return null;
  }

  // Time slot operations
  static Future<void> insertTimeSlot(TimeSlot timeSlot) async {
    try {
      print('📱 🔍 Inserting time slot into database:');
      print('📱 🔍   ID: ${timeSlot.id}');
      print('📱 🔍   Category ID: ${timeSlot.categoryId}');
      print('📱 🔍   Day: ${timeSlot.dayOfWeek} (${timeSlot.dayName})');
      print('📱 🔍   Time: ${timeSlot.startTime} - ${timeSlot.endTime}');

      final db = await database;
      final data = {
        'id': timeSlot.id,
        'category_id': timeSlot.categoryId,
        'day_of_week': timeSlot.dayOfWeek,
        'start_time': timeSlot.startTime.toString(),
        'end_time': timeSlot.endTime.toString(),
        'created_at': timeSlot.createdAt.toIso8601String(),
        'updated_at': timeSlot.createdAt.toIso8601String(), // Use createdAt since updatedAt doesn't exist
        'last_synced': DateTime.now().toIso8601String(),
      };

      print('📱 🔍 Data to insert: $data');

      await db.insert(
        _timeSlotsTable,
        data,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      print('📱 ✅ Time slot inserted successfully');

      // Verify insertion
      final verification = await db.query(
        _timeSlotsTable,
        where: 'id = ?',
        whereArgs: [timeSlot.id],
      );
      print('📱 🔍 Verification: Found ${verification.length} records for time slot ${timeSlot.id}');
    } catch (e) {
      print('📱 ❌ Error inserting time slot: $e');
      rethrow;
    }
  }

  static Future<List<TimeSlot>> getTimeSlotsByCategoryId(String categoryId) async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        _timeSlotsTable,
        where: 'category_id = ?',
        whereArgs: [categoryId],
      );

      print('🔍 Found ${maps.length} time slot records for category $categoryId');

      return List.generate(maps.length, (i) {
        try {
          final timeSlot = TimeSlot(
            id: maps[i]['id'],
            categoryId: maps[i]['category_id'],
            dayOfWeek: maps[i]['day_of_week'],
            startTime: CustomTimeOfDay.fromString(maps[i]['start_time']),
            endTime: CustomTimeOfDay.fromString(maps[i]['end_time']),
            createdAt: DateTime.parse(maps[i]['created_at']),
          );
          print('🔍   Created time slot: ${timeSlot.dayName} ${timeSlot.timeRange}');
          return timeSlot;
        } catch (e) {
          print('🔍 ❌ Error creating time slot from data: $e');
          print('🔍   Data: ${maps[i]}');
          rethrow;
        }
      });
    } catch (e) {
      print('🔍 ❌ Error querying time slots for category $categoryId: $e');
      return [];
    }
  }

  static Future<TimeSlot?> getTimeSlotById(String timeSlotId) async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        _timeSlotsTable,
        where: 'id = ?',
        whereArgs: [timeSlotId],
      );

      if (maps.isNotEmpty) {
        return TimeSlot(
          id: maps[0]['id'],
          categoryId: maps[0]['category_id'],
          dayOfWeek: maps[0]['day_of_week'],
          startTime: CustomTimeOfDay.fromString(maps[0]['start_time']),
          endTime: CustomTimeOfDay.fromString(maps[0]['end_time']),
          createdAt: DateTime.parse(maps[0]['created_at']),
        );
      }
      return null;
    } catch (e) {
      print('📱 ❌ Error getting time slot by ID: $e');
      return null;
    }
  }

  // User contacts (assignments) operations
  static Future<void> insertUserContact({
    required String id,
    required String userId,
    required String contactPhone,
    required String categoryId,
  }) async {
    final db = await database;
    await db.insert(
      _userContactsTable,
      {
        'id': id,
        'user_id': userId,
        'categorized_contact_phone': contactPhone,
        'assigned_category_id': categoryId,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'last_synced': null, // Will be set when synced to server
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  static Future<void> updateUserContactCategory({
    required String userId,
    required String contactPhone,
    required String categoryId,
  }) async {
    final db = await database;
    await db.update(
      _userContactsTable,
      {
        'assigned_category_id': categoryId,
        'updated_at': DateTime.now().toIso8601String(),
        'last_synced': null, // Mark as needing sync
      },
      where: 'user_id = ? AND categorized_contact_phone = ?',
      whereArgs: [userId, contactPhone],
    );
  }

  static Future<void> removeUserContactAssignment({
    required String userId,
    required String contactPhone,
  }) async {
    final db = await database;
    await db.delete(
      _userContactsTable,
      where: 'user_id = ? AND categorized_contact_phone = ?',
      whereArgs: [userId, contactPhone],
    );
  }

  static Future<String?> getAssignedCategoryId({
    required String userId,
    required String contactPhone,
  }) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _userContactsTable,
      columns: ['assigned_category_id'],
      where: 'user_id = ? AND categorized_contact_phone = ?',
      whereArgs: [userId, contactPhone],
    );

    if (maps.isNotEmpty) {
      return maps[0]['assigned_category_id'];
    }
    return null;
  }

  static Future<List<Map<String, dynamic>>> getUserContactsForSync(String userId) async {
    final db = await database;
    return await db.query(
      _userContactsTable,
      where: 'user_id = ? AND last_synced IS NULL',
      whereArgs: [userId],
    );
  }

  static Future<void> markUserContactSynced({
    required String userId,
    required String contactPhone,
  }) async {
    final db = await database;
    await db.update(
      _userContactsTable,
      {
        'last_synced': DateTime.now().toIso8601String(),
      },
      where: 'user_id = ? AND categorized_contact_phone = ?',
      whereArgs: [userId, contactPhone],
    );
  }

  // Sync queue operations
  static Future<void> addToSyncQueue({
    required String operationType,
    required String tableName,
    required String recordId,
    required Map<String, dynamic> data,
  }) async {
    final db = await database;
    await db.insert(_syncQueueTable, {
      'operation_type': operationType,
      'table_name': tableName,
      'record_id': recordId,
      'data': jsonEncode(data),
      'created_at': DateTime.now().toIso8601String(),
      'retry_count': 0,
    });
  }

  static Future<List<Map<String, dynamic>>> getPendingSyncOperations() async {
    final db = await database;
    return await db.query(
      _syncQueueTable,
      orderBy: 'created_at ASC',
    );
  }

  static Future<void> removeSyncOperation(int id) async {
    final db = await database;
    await db.delete(
      _syncQueueTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  static Future<void> incrementRetryCount(int id) async {
    final db = await database;

    // First get the current retry count
    final result = await db.query(
      _syncQueueTable,
      columns: ['retry_count'],
      where: 'id = ?',
      whereArgs: [id],
    );

    if (result.isNotEmpty) {
      final currentRetryCount = result.first['retry_count'] as int? ?? 0;
      await db.update(
        _syncQueueTable,
        {'retry_count': currentRetryCount + 1},
        where: 'id = ?',
        whereArgs: [id],
      );
    }
  }

  // Notification preferences operations
  static Future<void> insertNotificationPreference(NotificationPreference preference) async {
    final db = await database;
    await db.insert(
      _notificationPreferencesTable,
      {
        'id': preference.id,
        'user_id': preference.userId,
        'contact_user_id': preference.contactUserId,
        'time_slot_id': preference.timeSlotId,
        'is_enabled': preference.isEnabled ? 1 : 0,
        'created_at': preference.createdAt.toIso8601String(),
        'updated_at': preference.updatedAt.toIso8601String(),
        'last_synced': DateTime.now().toIso8601String(),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  static Future<List<NotificationPreference>> getNotificationPreferences({
    required String userId,
    required String contactUserId,
  }) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _notificationPreferencesTable,
      where: 'user_id = ? AND contact_user_id = ?',
      whereArgs: [userId, contactUserId],
    );

    return List.generate(maps.length, (i) {
      return NotificationPreference(
        id: maps[i]['id'],
        userId: maps[i]['user_id'],
        contactUserId: maps[i]['contact_user_id'],
        timeSlotId: maps[i]['time_slot_id'],
        isEnabled: maps[i]['is_enabled'] == 1,
        createdAt: DateTime.parse(maps[i]['created_at']),
        updatedAt: DateTime.parse(maps[i]['updated_at']),
      );
    });
  }

  static Future<List<NotificationPreference>> getAllUserNotificationPreferences(String userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _notificationPreferencesTable,
      where: 'user_id = ? AND is_enabled = 1',
      whereArgs: [userId],
    );

    return List.generate(maps.length, (i) {
      return NotificationPreference(
        id: maps[i]['id'],
        userId: maps[i]['user_id'],
        contactUserId: maps[i]['contact_user_id'],
        timeSlotId: maps[i]['time_slot_id'],
        isEnabled: maps[i]['is_enabled'] == 1,
        createdAt: DateTime.parse(maps[i]['created_at']),
        updatedAt: DateTime.parse(maps[i]['updated_at']),
      );
    });
  }

  static Future<void> updateNotificationPreference({
    required String userId,
    required String contactUserId,
    required String timeSlotId,
    required bool isEnabled,
  }) async {
    final db = await database;

    // Check if preference exists
    final existing = await db.query(
      _notificationPreferencesTable,
      where: 'user_id = ? AND contact_user_id = ? AND time_slot_id = ?',
      whereArgs: [userId, contactUserId, timeSlotId],
    );

    if (existing.isNotEmpty) {
      // Update existing
      await db.update(
        _notificationPreferencesTable,
        {
          'is_enabled': isEnabled ? 1 : 0,
          'updated_at': DateTime.now().toIso8601String(),
          'last_synced': null, // Mark as needing sync
        },
        where: 'user_id = ? AND contact_user_id = ? AND time_slot_id = ?',
        whereArgs: [userId, contactUserId, timeSlotId],
      );
    } else {
      // Insert new
      final preference = NotificationPreference(
        id: '', // Will be generated by server
        userId: userId,
        contactUserId: contactUserId,
        timeSlotId: timeSlotId,
        isEnabled: isEnabled,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await db.insert(
        _notificationPreferencesTable,
        {
          'id': DateTime.now().millisecondsSinceEpoch.toString(), // Temporary local ID
          'user_id': preference.userId,
          'contact_user_id': preference.contactUserId,
          'time_slot_id': preference.timeSlotId,
          'is_enabled': preference.isEnabled ? 1 : 0,
          'created_at': preference.createdAt.toIso8601String(),
          'updated_at': preference.updatedAt.toIso8601String(),
          'last_synced': null, // Mark as needing sync
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }
  }

  // Local notifications operations
  static Future<void> insertLocalNotification({
    required String contactPhone,
    required String contactName,
    required String timeSlotId,
    required DateTime scheduledTime,
  }) async {
    final db = await database;
    await db.insert(_notificationsTable, {
      'contact_phone': contactPhone,
      'contact_name': contactName,
      'time_slot_id': timeSlotId,
      'scheduled_time': scheduledTime.toIso8601String(),
      'created_at': DateTime.now().toIso8601String(),
      'synced': 0,
    });
  }

  static Future<List<Map<String, dynamic>>> getUnsyncedNotifications() async {
    final db = await database;
    return await db.query(
      _notificationsTable,
      where: 'synced = 0',
    );
  }

  static Future<void> markNotificationSynced(int id) async {
    final db = await database;
    await db.update(
      _notificationsTable,
      {'synced': 1},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Utility methods
  static Future<void> markRecordSynced(String tableName, String recordId) async {
    final db = await database;
    await db.update(
      tableName,
      {'last_synced': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [recordId],
    );
  }

  // Clean up duplicate categories
  static Future<void> cleanupDuplicateCategories() async {
    try {
      final db = await database;

      // Find duplicate categories (same user_id, type, and note)
      final duplicates = await db.rawQuery('''
        SELECT user_id, type, note, COUNT(*) as count, MIN(created_at) as first_created
        FROM $_categoriesTable
        GROUP BY user_id, type, note
        HAVING COUNT(*) > 1
      ''');

      print('📱 Found ${duplicates.length} sets of duplicate categories');

      for (final duplicate in duplicates) {
        final userId = duplicate['user_id'] as String;
        final type = duplicate['type'] as int;
        final note = duplicate['note'] as String;
        final firstCreated = duplicate['first_created'] as String;

        // Keep only the oldest category, delete the rest
        await db.delete(
          _categoriesTable,
          where: 'user_id = ? AND type = ? AND note = ? AND created_at != ?',
          whereArgs: [userId, type, note, firstCreated],
        );

        print('📱 Cleaned up duplicates for category type $type');
      }
    } catch (e) {
      print('📱 ❌ Error cleaning up duplicate categories: $e');
    }
  }

  // Debug method to check database contents
  static Future<void> debugDatabaseContents() async {
    try {
      final db = await database;

      // Check categories
      final categories = await db.query(_categoriesTable);
      print('🔍 DATABASE DEBUG: Found ${categories.length} categories');
      for (final category in categories) {
        print('🔍   Category: ${category['id']} - Type: ${category['type']} - Note: ${category['note']}');
      }

      // Check time slots
      final timeSlots = await db.query(_timeSlotsTable);
      print('🔍 DATABASE DEBUG: Found ${timeSlots.length} time slots');
      for (final slot in timeSlots) {
        print('🔍   Time Slot: ${slot['id']} - Category: ${slot['category_id']} - Day: ${slot['day_of_week']} - Time: ${slot['start_time']}-${slot['end_time']}');
      }

      // Check user contacts
      final userContacts = await db.query(_userContactsTable);
      print('🔍 DATABASE DEBUG: Found ${userContacts.length} user contacts');
      for (final contact in userContacts) {
        print('🔍   User Contact: ${contact['user_id']} -> ${contact['categorized_contact_phone']} = ${contact['assigned_category_id']}');
      }
    } catch (e) {
      print('🔍 ❌ Error debugging database contents: $e');
    }
  }

  static Future<void> clearAllData() async {
    final db = await database;
    await db.delete(_profilesTable);
    await db.delete(_categoriesTable);
    await db.delete(_timeSlotsTable);
    await db.delete(_userContactsTable);
    await db.delete(_notificationPreferencesTable);
    await db.delete(_syncQueueTable);
    await db.delete(_notificationsTable);
  }

  static Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
}
