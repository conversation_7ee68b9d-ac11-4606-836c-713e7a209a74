// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Contact Times';

  @override
  String get appSubtitle => 'Manage when you can be contacted';

  @override
  String get signIn => 'Sign In';

  @override
  String get signUp => 'Sign Up';

  @override
  String get signOut => 'Sign Out';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get fullName => 'Full Name';

  @override
  String get phoneNumber => 'Phone Number';

  @override
  String get username => 'Username';

  @override
  String get pleaseEnterEmail => 'Please enter your email';

  @override
  String get pleaseEnterValidEmail => 'Please enter a valid email';

  @override
  String get pleaseEnterPassword => 'Please enter your password';

  @override
  String get pleaseEnterFullName => 'Please enter your full name';

  @override
  String get pleaseEnterPhoneNumber => 'Please enter your phone number';

  @override
  String get pleaseEnterUsername => 'Please enter your username';

  @override
  String get passwordsDoNotMatch => 'Passwords do not match';

  @override
  String get pleaseConfirmPassword => 'Please confirm your password';

  @override
  String get passwordMustBeAtLeast6Characters =>
      'Password must be at least 6 characters';

  @override
  String get pleaseEnterValidPhoneNumber => 'Please enter a valid phone number';

  @override
  String get createAccount => 'Create Account';

  @override
  String get joinContactTimesToday => 'Join Contact Times today';

  @override
  String get phoneNumberAlreadyRegistered => 'Phone Number Already Registered';

  @override
  String get thisPhoneNumberIsAlreadyRegistered =>
      'This phone number is already associated with an account. Would you like to sign in instead?';

  @override
  String get emailAlreadyRegistered => 'Email Already Registered';

  @override
  String get thisEmailIsAlreadyRegistered =>
      'This email is already associated with an account. Would you like to sign in instead?';

  @override
  String get invalidEmail => 'Invalid Email';

  @override
  String get weakPassword => 'Weak Password';

  @override
  String get pleaseChooseStrongerPassword =>
      'Please choose a stronger password with at least 6 characters';

  @override
  String get accountSetupFailed => 'Account Setup Failed';

  @override
  String get accountCreatedButProfileSetupFailed =>
      'Your account was created but we couldn\'t set up your profile. Please contact support';

  @override
  String get signUpFailedTitle => 'Sign Up Failed';

  @override
  String get unexpectedErrorOccurred =>
      'An unexpected error occurred. Please try again';

  @override
  String get signInFailed => 'Sign in failed';

  @override
  String get signUpFailed => 'Sign up failed';

  @override
  String get profileUpdateFailed => 'Failed to update profile';

  @override
  String get profileUpdatedSuccessfully => 'Profile updated successfully';

  @override
  String get dontHaveAccount => 'Don\'t have an account? Sign Up';

  @override
  String get alreadyHaveAccount => 'Already have an account? Sign In';

  @override
  String get contacts => 'Contacts';

  @override
  String get categories => 'Categories';

  @override
  String get distribute => 'Distribute';

  @override
  String get profile => 'Profile';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get loadingContacts => 'Loading contacts...';

  @override
  String get noContactsFound => 'No contacts found';

  @override
  String get contactsPermissionRequired =>
      'Contacts permission is required to sync your contacts';

  @override
  String get grantPermission => 'Grant Permission';

  @override
  String get syncContacts => 'Sync Contacts';

  @override
  String get availableTimes => 'Available Times';

  @override
  String get timeSlots => 'Time Slots';

  @override
  String get addTimeSlot => 'Add Time Slot';

  @override
  String get editTimeSlots => 'Edit Time Slots';

  @override
  String get saveTimeSlots => 'Save Time Slots';

  @override
  String get startTime => 'Start Time';

  @override
  String get endTime => 'End Time';

  @override
  String get selectDay => 'Select Day';

  @override
  String get monday => 'Monday';

  @override
  String get tuesday => 'Tuesday';

  @override
  String get wednesday => 'Wednesday';

  @override
  String get thursday => 'Thursday';

  @override
  String get friday => 'Friday';

  @override
  String get saturday => 'Saturday';

  @override
  String get sunday => 'Sunday';

  @override
  String get activeNow => 'ACTIVE NOW';

  @override
  String get notify => 'Notify';

  @override
  String get call => 'Call';

  @override
  String get message => 'Message';

  @override
  String get profileInformation => 'Profile Information';

  @override
  String get appInformation => 'App Information';

  @override
  String get version => 'Version';

  @override
  String get buildNumber => 'Build Number';

  @override
  String get notificationSettings => 'Notification Settings';

  @override
  String get changePhoto => 'Change Photo';

  @override
  String get updateProfile => 'Update Profile';

  @override
  String get aboutContactTimes => 'About Contact Times';

  @override
  String get manageWhenYouCanBeContacted =>
      'Manage when you can be contacted by different people. Set up categories and time slots to let others know your availability preferences.';

  @override
  String get loadingProfile => 'Loading Profile...';

  @override
  String get profileNotFound => 'Profile not found';

  @override
  String get profilePictureUploadComingSoon =>
      'Profile picture upload coming soon';

  @override
  String get noPhoneNumber => 'No phone number';

  @override
  String get welcome => 'Welcome!';

  @override
  String get failedToSignOut => 'Failed to sign out';

  @override
  String get uploadProfilePicture => 'Upload Profile Picture';

  @override
  String get selectImageSource => 'Select Image Source';

  @override
  String get camera => 'Camera';

  @override
  String get gallery => 'Gallery';

  @override
  String get imageUploadedSuccessfully =>
      'Profile picture updated successfully';

  @override
  String get failedToUploadImage => 'Failed to upload image';

  @override
  String get deleteAccount => 'Delete Account';

  @override
  String get deleteAccountConfirmation =>
      'Are you sure you want to delete your account? This action cannot be undone.';

  @override
  String get deleteAccountWarning =>
      'This will permanently delete your account and all associated data.';

  @override
  String get confirmDelete => 'Confirm Delete';

  @override
  String get accountDeletedSuccessfully => 'Account deleted successfully';

  @override
  String get failedToDeleteAccount => 'Failed to delete account';

  @override
  String get enterYourFullName => 'Enter your full name';

  @override
  String get enterYourUsername => 'Enter your username';

  @override
  String get enterYourPhoneNumber => 'Enter your phone number';

  @override
  String get language => 'Language';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get english => 'English';

  @override
  String get arabic => 'العربية';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get done => 'Done';

  @override
  String get back => 'Back';

  @override
  String get next => 'Next';

  @override
  String get previous => 'Previous';

  @override
  String get close => 'Close';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get warning => 'Warning';

  @override
  String get info => 'Info';

  @override
  String get offline => 'Offline';

  @override
  String get online => 'Online';

  @override
  String get syncing => 'Syncing...';

  @override
  String get syncComplete => 'Sync Complete';

  @override
  String get noTimeSlots => 'No time slots configured';

  @override
  String get addFirstTimeSlot => 'Add your first time slot';

  @override
  String get timeSlotOverlap => 'Time slots overlap and will be merged';

  @override
  String get categoryAssigned => 'Category assigned';

  @override
  String get categoryUpdated => 'Category updated';

  @override
  String get contactCategorized => 'Contact categorized successfully';

  @override
  String get loading => 'Loading...';

  @override
  String get retry => 'Retry';

  @override
  String get refresh => 'Refresh';

  @override
  String get myCategories => 'My Categories';

  @override
  String get pending => 'Pending';

  @override
  String get loadingYourCategories => 'Loading your categories...';

  @override
  String get oopsSomethingWentWrong => 'Oops! Something went wrong';

  @override
  String get tryAgain => 'Try Again';

  @override
  String get categoryNoteUpdatedSuccessfully =>
      'Category note updated successfully';

  @override
  String get updatedLocally => 'Updated locally. Will sync when online.';

  @override
  String get updatedOffline => 'Updated offline. Will sync when online.';

  @override
  String get failedToUpdateCategory => 'Failed to update category';

  @override
  String get timeSlotsUpdatedSuccessfully => 'Time slots updated successfully';

  @override
  String get allChangesSyncedSuccessfully => 'All changes synced successfully';

  @override
  String get noDescriptionAddedYet => 'No description added yet...';

  @override
  String todaysTimeSlots(int count) {
    return 'Today\'s Time Slots ($count)';
  }

  @override
  String timeSlotsCount(int count) {
    return 'Time Slots ($count)';
  }

  @override
  String get perfectTimeToContact => 'Perfect time to contact!';

  @override
  String andMoreSlotsToday(int count) {
    return '... and $count more slots today';
  }

  @override
  String noTimeSlotsForToday(int count, String plural) {
    return 'No time slots for today. You have $count slot$plural on other days.';
  }

  @override
  String get noTimeSlotsConfiguredYet => 'No time slots configured yet';

  @override
  String editCategory(String categoryName) {
    return 'Edit $categoryName';
  }

  @override
  String get description => 'Description';

  @override
  String get addDescriptionForCategory =>
      'Add a description for this category...';

  @override
  String timeSlotNumber(int number) {
    return 'Time Slot $number';
  }

  @override
  String get dayOfWeek => 'Day of Week';

  @override
  String get noTimeSlotsYet => 'No Time Slots Yet';

  @override
  String get addYourFirstTimeSlot =>
      'Add your first time slot to get started.\nLet others know when you\'re available!';

  @override
  String get addYourFirstTimeSlotButton => 'Add Your First Time Slot';

  @override
  String get timeSlotOverlapWarning =>
      'This slot overlaps with others and will be merged when saved';

  @override
  String get saving => 'Saving...';

  @override
  String get saveChanges => 'Save Changes';

  @override
  String get pleaseAddAtLeastOneTimeSlot => 'Please add at least one time slot';

  @override
  String mergedOverlappingTimeSlots(int count, String plural) {
    return 'Merged $count overlapping time slot$plural';
  }

  @override
  String failedToSaveTimeSlots(String error) {
    return 'Failed to save time slots: $error';
  }

  @override
  String timeSlotsCountSimple(int count, String plural) {
    return '$count time slot$plural';
  }

  @override
  String get hint => 'Hint';

  @override
  String get enterYourFullNameHint => 'Enter your full name';

  @override
  String get enterYourUsernameHint => 'Enter your username';

  @override
  String get enterYourPhoneNumberHint => 'Enter your phone number';

  @override
  String get contactsPermissionDenied => 'Contacts permission denied';

  @override
  String get contactsSynced => 'Contacts synced successfully';

  @override
  String get failedToSyncContacts => 'Failed to sync contacts';

  @override
  String get searchContacts => 'Search contacts...';

  @override
  String get noContactsAvailable => 'No contacts available';

  @override
  String get tapToAssignCategory => 'Tap to assign category';

  @override
  String categoryAssignedTo(String contactName) {
    return 'Category assigned to $contactName';
  }

  @override
  String get timeSlot => 'Time Slot';

  @override
  String get noTimeSlotsConfigured => 'No time slots configured';

  @override
  String get overlappingTimeSlots => 'Overlapping time slots will be merged';

  @override
  String get invalidTimeSlot => 'Invalid time slot';

  @override
  String get startTimeMustBeBeforeEndTime =>
      'Start time must be before end time';

  @override
  String invalidTimeSlotRange(String startTime, String endTime) {
    return 'Invalid time range: Start time ($startTime) must be before end time ($endTime)';
  }

  @override
  String get distributeContacts => 'Distribute Contacts';

  @override
  String get assignContactsToCategories => 'Assign contacts to categories';

  @override
  String get swipeToAssign => 'Swipe to assign category';

  @override
  String get skipContact => 'Skip Contact';

  @override
  String get assignToCategory => 'Assign to Category';

  @override
  String get organizeYourContactsIntoCategories =>
      'Organize your contacts into categories';

  @override
  String contactsToCategorizePlural(Object count) {
    return '$count contacts to categorize';
  }

  @override
  String contactAssignedToCategory(String contactName, String categoryName) {
    return '$contactName assigned to \"$categoryName\"';
  }

  @override
  String contactAllNumbersAssignedToCategory(
      Object categoryName, Object contactName, Object phoneCount) {
    return '$contactName (all $phoneCount numbers) assigned to $categoryName';
  }

  @override
  String contactPartialNumbersAssignedToCategory(Object categoryName,
      Object contactName, Object phoneCount, Object successCount) {
    return '$contactName ($successCount of $phoneCount numbers) assigned to $categoryName';
  }

  @override
  String get refreshContacts => 'Refresh contacts';

  @override
  String get allDone => 'All Done! 🎉';

  @override
  String get allYourContactsHaveBeenCategorized =>
      'All your contacts have been categorized!';

  @override
  String get greatJobOrganizingYourContacts =>
      'Great job organizing your contacts.';

  @override
  String get allContactsCategorized => 'All contacts categorized!';

  @override
  String get categoryDescriptions => 'Category Descriptions';

  @override
  String get assigningContact => 'Assigning Contact';

  @override
  String assigningAllPhoneNumbers(
      int count, String contactName, String categoryName) {
    return 'Assigning all $count phone numbers for $contactName to $categoryName...';
  }

  @override
  String get partialAssignment => 'Partial Assignment';

  @override
  String successfullyAssignedPhoneNumbers(
      int successCount, String contactName, int failedCount) {
    return 'Successfully assigned $successCount phone numbers for $contactName, but $failedCount failed. The contact has been moved from the uncategorized list.';
  }

  @override
  String get retryFailed => 'Retry Failed';

  @override
  String get connectionError => 'Connection Error';

  @override
  String get pleaseCheckYourInternetConnection =>
      'Please check your internet connection and try again.';

  @override
  String get requestTimeout => 'Request Timeout';

  @override
  String get requestTookTooLong =>
      'The request took too long. Please try again.';

  @override
  String get assignmentFailed => 'Assignment Failed';

  @override
  String failedToAssignContactToCategory(
      String contactName, String categoryName) {
    return 'Failed to assign $contactName to $categoryName. Please try again.';
  }

  @override
  String get ok => 'OK';

  @override
  String get noInternetConnection => 'No Internet Connection';

  @override
  String get noInternetMessage =>
      'Please check your internet connection and try again';

  @override
  String get categoryEditRequiresInternet =>
      'Category editing requires an internet connection to sync with other users. Please connect to the internet and try again.';

  @override
  String get emergencyModeRequiresInternet =>
      'Emergency mode requires an internet connection to notify other users. Please connect to the internet and try again.';

  @override
  String get checkConnection => 'Check Connection';

  @override
  String get aboutApp => 'About App';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get termsOfService => 'Terms of Service';

  @override
  String get notifications => 'Notifications';

  @override
  String get enableNotifications => 'Enable Notifications';

  @override
  String get disableNotifications => 'Disable Notifications';

  @override
  String get notificationPermissionRequired =>
      'Notification permission is required';

  @override
  String get notificationsEnabled => 'Notifications enabled';

  @override
  String get notificationsDisabled => 'Notifications disabled';

  @override
  String get calling => 'Calling';

  @override
  String get callContact => 'Call Contact';

  @override
  String get sendMessage => 'Send Message';

  @override
  String get contactInfo => 'Contact Info';

  @override
  String get noContactInfo => 'No contact information available';

  @override
  String get editCategories => 'Edit Categories';

  @override
  String get categorySettings => 'Category Settings';

  @override
  String get categoryDescription => 'Category Description';

  @override
  String get addCategoryDescription => 'Add a description for this category';

  @override
  String get noCategoriesFound => 'No categories found';

  @override
  String get settings => 'Settings';

  @override
  String get preferences => 'Preferences';

  @override
  String get account => 'Account';

  @override
  String get security => 'Security';

  @override
  String get help => 'Help';

  @override
  String get support => 'Support';

  @override
  String get feedback => 'Feedback';

  @override
  String get confirm => 'Confirm';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get apply => 'Apply';

  @override
  String get reset => 'Reset';

  @override
  String get clear => 'Clear';

  @override
  String get search => 'Search';

  @override
  String get filter => 'Filter';

  @override
  String get sort => 'Sort';

  @override
  String get today => 'Today';

  @override
  String get yesterday => 'Yesterday';

  @override
  String get tomorrow => 'Tomorrow';

  @override
  String get thisWeek => 'This Week';

  @override
  String get lastWeek => 'Last Week';

  @override
  String get nextWeek => 'Next Week';

  @override
  String get morning => 'Morning';

  @override
  String get afternoon => 'Afternoon';

  @override
  String get evening => 'Evening';

  @override
  String get night => 'Night';

  @override
  String get am => 'AM';

  @override
  String get pm => 'PM';

  @override
  String get selectTime => 'Select Time';

  @override
  String get selectDate => 'Select Date';

  @override
  String get selectCategory => 'Select Category';

  @override
  String get selectContact => 'Select Contact';

  @override
  String get update => 'Update';

  @override
  String get updated => 'Updated';

  @override
  String get create => 'Create';

  @override
  String get created => 'Created';

  @override
  String get remove => 'Remove';

  @override
  String get removed => 'Removed';

  @override
  String get add => 'Add';

  @override
  String get added => 'Added';

  @override
  String get peopleUsingContactTimes => 'People using Contact Times';

  @override
  String get permissionRequired => 'Permission Required';

  @override
  String get somethingWentWrong => 'Something went wrong';

  @override
  String get openSettings => 'Open Settings';

  @override
  String get noContactsUsingApp => 'No contacts using the app';

  @override
  String get noContactsUsingAppDescription =>
      'None of your contacts are using Contact Times yet.\n\nTo see contacts here:\n• Invite friends to download the app\n• Make sure they sign up with their phone numbers\n• Their numbers should be in your device contacts';

  @override
  String contactsCount(int count, String plural) {
    return '$count contact$plural';
  }

  @override
  String get assignCategory => 'Assign Category';

  @override
  String get loadingEllipsis => 'Loading...';

  @override
  String errorLoadingCategories(String error) {
    return 'Error loading categories: $error';
  }

  @override
  String get assignmentMayHaveFailed =>
      'Assignment may have failed - please try again';

  @override
  String errorAssigningCategory(String error) {
    return 'Error assigning category: $error';
  }

  @override
  String get viewDetails => 'View details';

  @override
  String failedToMakeCall(String error) {
    return 'Failed to make call: $error';
  }

  @override
  String get notificationEnabledOffline =>
      'Notification enabled for this time slot (offline)';

  @override
  String get notificationDisabledOffline =>
      'Notification disabled for this time slot (offline)';

  @override
  String get notificationEnabled => 'Notification enabled for this time slot';

  @override
  String get notificationDisabled => 'Notification disabled for this time slot';

  @override
  String get errorUpdatingNotification => 'Error updating notification';

  @override
  String get notificationPreferenceExists =>
      'Notification preference already exists. Please try again.';

  @override
  String get networkErrorCheckConnection =>
      'Network error. Please check your connection and try again.';

  @override
  String callContactQuestion(String contactName) {
    return 'Call $contactName?';
  }

  @override
  String get goodTimeToCall => 'Good time to call';

  @override
  String get notIdealTiming => 'Not ideal timing';

  @override
  String get theirPreferredTimes => 'Their preferred times:';

  @override
  String get noTimeSlotsAvailable => 'No time slots available';

  @override
  String get callNow => 'Call Now';

  @override
  String get callAnyway => 'Call Anyway';

  @override
  String get activeNowLabel => 'ACTIVE NOW';

  @override
  String get on => 'ON';

  @override
  String get off => 'OFF';

  @override
  String chooseHowToCategorize(String contactName) {
    return 'Choose how to categorize $contactName';
  }

  @override
  String get availableCategories => 'Available Categories:';

  @override
  String get loadingContactDetails => 'Loading contact details...';

  @override
  String get contact => 'Contact';

  @override
  String contactPreferences(String contactName) {
    return '$contactName\'s Preferences';
  }

  @override
  String get communicationPreferences => 'Communication preferences';

  @override
  String get callingSuggestion => 'Calling Suggestion';

  @override
  String get noPreferencesSet => 'No preferences set';

  @override
  String get contactHasntSetPreferences =>
      'This contact hasn\'t set communication preferences for you yet.';

  @override
  String get currentUserProfileNotFound => 'Current user profile not found';

  @override
  String errorUpdatingNotificationWithDetails(String error) {
    return 'Error updating notification: $error';
  }

  @override
  String get categoryContactAnytime => 'Anytime';

  @override
  String get categoryPreferAnytime => 'If It Can Wait';

  @override
  String get categoryContactAtTimes => 'Preferred Times';

  @override
  String get categoryContactThroughMessages => 'Messages Only';

  @override
  String get categoryNoteContactAnytime =>
      'Feel free to contact me at any time.';

  @override
  String get categoryNotePreferAnytime =>
      'You can reach out anytime if it\'s not urgent, but I prefer to be contacted during the times below when possible.';

  @override
  String get categoryNoteContactAtTimes =>
      'Please contact me during the preferred time slots listed below.';

  @override
  String get categoryNoteContactThroughMessages =>
      'I prefer to be contacted by message only, and during the times shown below.';

  @override
  String get callingSuggestionNoCategory =>
      'This contact hasn\'t set up Contact Times yet.';

  @override
  String get callingSuggestionContactAnytime =>
      'You can contact them at any time.';

  @override
  String get callingSuggestionPreferAnytimeGoodTime =>
      'Now is a preferred time to call.';

  @override
  String get callingSuggestionPreferAnytimeBadTime =>
      'You can call now, but they prefer calls during their specified times.';

  @override
  String get callingSuggestionContactAtTimesGoodTime =>
      'Now is a good time to call.';

  @override
  String get callingSuggestionContactAtTimesBadTime =>
      'Please call during their specified times.';

  @override
  String get callingSuggestionContactThroughMessages =>
      'They prefer to be contacted through messages.';

  @override
  String get contactSuggestionNoCategory =>
      'This contact hasn\'t set communication preferences yet. You can contact them anytime.';

  @override
  String contactSuggestionContactAnytimeGood(String note) {
    return '✅ Great time to contact! $note';
  }

  @override
  String get contactSuggestionPreferAnytimeGoodTime =>
      '✅ Perfect time to contact! You\'re calling during their preferred hours.';

  @override
  String get contactSuggestionPreferAnytimeBadTime =>
      '⚠️ You can contact them now, but they prefer calls during their specified times.';

  @override
  String get contactSuggestionContactAtTimesGoodTime =>
      '✅ Perfect time to contact! You\'re calling during their available hours.';

  @override
  String get contactSuggestionContactAtTimesBadTime =>
      '❌ Not the best time. They prefer to be contacted during their specified times only.';

  @override
  String get contactSuggestionContactThroughMessages =>
      '💬 They prefer messages over calls. Consider sending a text instead.';

  @override
  String get azNavigation => 'أ-Z';

  @override
  String get recentSearches => 'Recent searches';

  @override
  String get noSearchResults => 'No search results';

  @override
  String noSearchResultsDescription(String query) {
    return 'No contacts found matching \"$query\".\n\nTry:\n• Checking the spelling\n• Using a different search term\n• Searching by name or phone number';
  }

  @override
  String get clearSearch => 'Clear search';

  @override
  String searchResults(int count, int total) {
    return '$count of $total contacts';
  }

  @override
  String get jumpToLetter => 'Jump to Letter';

  @override
  String get share => 'Share';

  @override
  String get shareApp => 'Share App';

  @override
  String chooseHowToShare(String contactName) {
    return 'Choose how you\'d like to share the app with $contactName:';
  }

  @override
  String get whatsapp => 'WhatsApp';

  @override
  String get sms => 'SMS';

  @override
  String get otherApps => 'Other Apps';

  @override
  String get copyLink => 'Copy Link';

  @override
  String get whatsappOpenedWithMessage =>
      'WhatsApp opened. Message copied to clipboard - paste it to share!';

  @override
  String shareMessage(String appLink) {
    return '📱 I\'m using Contact Time to manage when people can call or message me — no more missed or mistimed calls!\n\nWant to know the best time to reach me? Download Contact Time and search for my profile to see when I\'m available.\n\n👉 $appLink\n\nSet your own contact preferences too — it\'s easy and stress-free.';
  }

  @override
  String get notUsingTheApp => 'Not using the app';

  @override
  String contactNotUsingAppYet(String contactName) {
    return '$contactName is not using this app yet.';
  }

  @override
  String get whatWouldYouLikeToDo => 'What would you like to do?';

  @override
  String get selectPhoneNumber => 'Select Phone Number';

  @override
  String get choosePhoneNumberToCall => 'Choose which phone number to call:';

  @override
  String get profileUpdatedOffline =>
      'Profile updated offline. Will sync when online.';

  @override
  String get offlineMode => 'Offline Mode';

  @override
  String get profileUpdateOnlineOnly =>
      'Profile updates and image uploads are only available when you\'re connected to the internet. Please check your connection and try again.';

  @override
  String get backOnline =>
      'You\'re back online! You can now update your profile.';

  @override
  String get stillOffline =>
      'Still offline. Please check your internet connection.';

  @override
  String get emergencyMode => 'Emergency Mode';

  @override
  String get activateEmergencyMode => 'Activate Emergency Mode';

  @override
  String get emergencyModeActive => 'Emergency Mode Active';

  @override
  String get emergencyModeDescription =>
      'During emergency mode, contacts will see a special message when trying to call or message you.';

  @override
  String get selectDuration => 'Select Duration:';

  @override
  String get hour1 => 'Hour 1';

  @override
  String get hours3 => 'Hours 3';

  @override
  String get hours6 => 'Hours 6';

  @override
  String get hours12 => 'Hours 12';

  @override
  String get day1 => 'Day 1';

  @override
  String get week1 => 'Week 1';

  @override
  String get understood => 'Understood';

  @override
  String get notAvailable => 'not available';

  @override
  String get personalEmergency => 'personal emergency';

  @override
  String emergencyModeActiveFor(String contactName) {
    return 'Emergency Mode Active for $contactName';
  }

  @override
  String contactNotAvailable(String contactName) {
    return '$contactName is not available';
  }

  @override
  String emergencyModeMessage(
      String userName, String exceptionGroups, String remainingTime) {
    return '$userName is currently in emergency mode. They are not available for calls or messages. $exceptionGroups. Emergency mode: $remainingTime';
  }

  @override
  String get selectExceptionGroups => 'Select Exception Groups';

  @override
  String get exceptionGroupsDescription =>
      'Select groups that can still contact you during emergency mode';

  @override
  String get addCustomGroup => 'Create New Group or Note';

  @override
  String get addCustomGroupDescription =>
      'You can create custom groups (e.g., \'Work Team\') or personal notes (e.g., \'Important Meeting\')';

  @override
  String get groupName => 'Group/Note name';

  @override
  String get family => 'Family';

  @override
  String get coworkers => 'Coworkers';

  @override
  String get friends => 'Friends';

  @override
  String daysRemaining(int days, int hours) {
    return '${days}d ${hours}h remaining';
  }

  @override
  String hoursRemaining(int hours, int minutes) {
    return '${hours}h ${minutes}m remaining';
  }

  @override
  String minutesRemaining(int minutes) {
    return '${minutes}m remaining';
  }

  @override
  String get emergencyContactsMessage =>
      'Contacts will see an emergency message when trying to reach you';

  @override
  String get deactivateEmergencyMode => 'Deactivate Emergency Mode';

  @override
  String get cannotMakeCallsInEmergency =>
      'You cannot make calls while in emergency mode. Please deactivate emergency mode first';

  @override
  String cannotContact(String contactName) {
    return 'Cannot contact $contactName';
  }

  @override
  String get remaining => 'remaining';

  @override
  String get seeLess => 'see less';

  @override
  String get tapToActivateEmergencyMode => 'Tap to activate emergency mode';

  @override
  String get selectCountry => 'Select Country';

  @override
  String get searchCountries => 'Search countries...';
}
