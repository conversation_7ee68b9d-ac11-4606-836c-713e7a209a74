import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'services/supabase_service.dart';
import 'services/calling_service.dart';
import 'services/notification_service.dart';
import 'services/connectivity_service.dart';
import 'services/sync_service.dart';
import 'services/offline_contact_service.dart';
import 'services/category_assignment_service.dart';
import 'services/assigned_categories_service.dart';
import 'services/language_service.dart';
import 'screens/auth/sign_in_screen.dart';
import 'screens/dashboard/dashboard_screen.dart';
import 'l10n/app_localizations.dart';

// Global navigation key for handling notification taps
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await SupabaseService.initialize();
  await CallingService.initialize();

  // Initialize Notifications
  await NotificationService.initialize();
  // Note: Permissions will be requested when user first enables notifications

  // Initialize offline services
  try {
    print('🚀 Initializing offline services...');
    await ConnectivityService().initialize();
    await SyncService().initialize();
    await OfflineContactService().initialize();
    await CategoryAssignmentService.initialize();
    await AssignedCategoriesService.initialize();
    await LanguageService().initialize();
    print('✅ Offline services initialized successfully');
  } catch (e) {
    print('❌ Failed to initialize offline services: $e');
    // Continue anyway - app should still work online
  }

  runApp(const ContactTimesApp());
}

class ContactTimesApp extends StatelessWidget {
  const ContactTimesApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: LanguageService(),
      builder: (context, child) {
        final languageService = LanguageService();
        return MaterialApp(
          title: 'Contact Times',
          navigatorKey: navigatorKey,
          locale: languageService.currentLocale,
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('en'),
            Locale('ar'),
          ],
          // Set text direction based on current language
          builder: (context, child) {
            return Directionality(
              textDirection: languageService.textDirection,
              child: child!,
            );
          },
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
            useMaterial3: true,
          ),
          home: const AuthWrapper(),
          debugShowCheckedModeBanner: false,
        );
      },
    );
  }
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _isAuthenticated = false;
  bool _isLoading = true;
  bool _hasCheckedInitialAuth = false;
  late final StreamSubscription<AuthState> _authSubscription;

  @override
  void initState() {
    super.initState();
    _checkInitialAuthState();
    _listenToAuthChanges();
  }

  @override
  void dispose() {
    _authSubscription.cancel();
    super.dispose();
  }

  void _checkInitialAuthState() {
    // Check if user is already authenticated
    final currentUser = SupabaseService.currentUser;
    print('🔐 Initial auth check - currentUser: ${currentUser?.id}');

    if (mounted) {
      setState(() {
        _isAuthenticated = currentUser != null;
        _isLoading = false;
        _hasCheckedInitialAuth = true;
      });
    }
  }

  void _listenToAuthChanges() {
    _authSubscription =
        SupabaseService.authStateChanges.listen((AuthState authState) {
      print('🔐 Auth state changed - session: ${authState.session?.user.id}');

      // Only update auth state if widget is still mounted
      if (!mounted) return;

      // Only update auth state if we're online or if this is a sign-in event
      final connectivityService = ConnectivityService();
      final isOnline = connectivityService.isOnline;

      if (isOnline || authState.session != null) {
        // Update auth state when online, or when signing in
        setState(() {
          _isAuthenticated = authState.session != null;
          _isLoading = false;
        });
      } else if (!isOnline && _hasCheckedInitialAuth) {
        // When offline, preserve the current auth state
        print(
            '🔐 Device is offline - preserving current auth state: $_isAuthenticated');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_isAuthenticated) {
      return const DashboardScreen();
    } else {
      return const SignInScreen();
    }
  }
}
