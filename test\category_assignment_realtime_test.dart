import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:contact_times/services/offline_contact_service.dart';
import 'package:contact_times/models/models.dart' as models;

void main() {
  group('Real-time Category Assignment Tests', () {
    late OfflineContactService offlineService;

    setUp(() async {
      // Clear SharedPreferences before each test
      SharedPreferences.setMockInitialValues({});
      offlineService = OfflineContactService();
    });

    tearDown(() async {
      offlineService.dispose();
    });

    test('Category assignment cache should update in real-time', () async {
      // Test that category assignments are cached and updated in real-time
      
      final testPhone = '+1234567890';
      final cleanedPhone = '1234567890';
      
      // Create a test category
      final testCategory = models.Category(
        id: 'test-category-123',
        userId: 'test-user-456',
        type: models.CategoryType.family,
        note: 'Test family category',
        timeSlots: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Test that cache is initially empty
      final initialAssignment = offlineService.getCachedContactAssignment(testPhone);
      expect(initialAssignment, isNull);

      // Simulate real-time cache update
      offlineService.updateContactAssignment(testPhone, testCategory);

      // Test that cache is updated
      final updatedAssignment = offlineService.getCachedContactAssignment(testPhone);
      expect(updatedAssignment, isNotNull);
      expect(updatedAssignment?.id, equals(testCategory.id));
      expect(updatedAssignment?.type, equals(models.CategoryType.family));

      // Test that phone variations are also cached
      final cleanedPhoneAssignment = offlineService.getCachedContactAssignment(cleanedPhone);
      expect(cleanedPhoneAssignment, isNotNull);
      expect(cleanedPhoneAssignment?.id, equals(testCategory.id));
    });

    test('Category assignment should be removed from cache when deleted', () async {
      // Test that category assignments are properly removed from cache
      
      final testPhone = '+1234567890';
      
      // Create a test category
      final testCategory = models.Category(
        id: 'test-category-789',
        userId: 'test-user-456',
        type: models.CategoryType.coWorkers,
        note: 'Test co-workers category',
        timeSlots: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Add to cache
      offlineService.updateContactAssignment(testPhone, testCategory);
      
      // Verify it's cached
      final cachedAssignment = offlineService.getCachedContactAssignment(testPhone);
      expect(cachedAssignment, isNotNull);

      // Remove from cache (simulate deletion)
      offlineService.updateContactAssignment(testPhone, null);

      // Verify it's removed
      final removedAssignment = offlineService.getCachedContactAssignment(testPhone);
      expect(removedAssignment, isNull);
    });

    test('Category assignment stream should emit updates', () async {
      // Test that the stream emits updates when assignments change
      
      final testPhone = '+9876543210';
      final testCategory = models.Category(
        id: 'test-category-stream',
        userId: 'test-user-stream',
        type: models.CategoryType.friends,
        note: 'Test friends category',
        timeSlots: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      bool streamUpdateReceived = false;
      Map<String, models.Category?>? receivedAssignments;

      // Listen to the stream
      final subscription = offlineService.contactAssignmentsStream.listen((assignments) {
        streamUpdateReceived = true;
        receivedAssignments = assignments;
      });

      // Update assignment
      offlineService.updateContactAssignment(testPhone, testCategory);

      // Wait a bit for stream to emit
      await Future.delayed(Duration(milliseconds: 100));

      // Verify stream update was received
      expect(streamUpdateReceived, isTrue);
      expect(receivedAssignments, isNotNull);
      expect(receivedAssignments!.containsKey(testPhone), isTrue);
      expect(receivedAssignments![testPhone]?.id, equals(testCategory.id));

      // Clean up
      await subscription.cancel();
    });

    test('Phone number variations should be handled correctly', () async {
      // Test that different phone number formats are handled properly
      
      final originalPhone = '+****************';
      final cleanedPhone = '15551234567';
      final anotherVariation = '5551234567';
      
      final testCategory = models.Category(
        id: 'test-category-variations',
        userId: 'test-user-variations',
        type: models.CategoryType.family,
        note: 'Test variations category',
        timeSlots: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Update with original phone format
      offlineService.updateContactAssignment(originalPhone, testCategory);

      // Test that all variations can retrieve the assignment
      final originalAssignment = offlineService.getCachedContactAssignment(originalPhone);
      final cleanedAssignment = offlineService.getCachedContactAssignment(cleanedPhone);
      final variationAssignment = offlineService.getCachedContactAssignment(anotherVariation);

      expect(originalAssignment, isNotNull);
      expect(cleanedAssignment, isNotNull);
      expect(variationAssignment, isNotNull);

      expect(originalAssignment?.id, equals(testCategory.id));
      expect(cleanedAssignment?.id, equals(testCategory.id));
      expect(variationAssignment?.id, equals(testCategory.id));
    });

    test('Real-time cache should be checked first in getContactCategoryWithPhones', () async {
      // Test that the real-time cache is prioritized over other data sources
      
      final contactUserId = 'contact-user-123';
      final callerPhone = '+1234567890';
      final contactPhoneNumbers = ['+0987654321'];
      
      final testCategory = models.Category(
        id: 'test-category-priority',
        userId: contactUserId,
        type: models.CategoryType.family,
        note: 'Test priority category',
        timeSlots: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Add to real-time cache
      offlineService.updateContactAssignment(callerPhone, testCategory);

      // Call getContactCategoryWithPhones
      final result = await offlineService.getContactCategoryWithPhones(
        contactUserId: contactUserId,
        callerPhone: callerPhone,
        contactPhoneNumbers: contactPhoneNumbers,
      );

      // Should return the cached category
      expect(result, isNotNull);
      expect(result?.id, equals(testCategory.id));
      expect(result?.type, equals(models.CategoryType.family));
    });

    test('Offline category dialog should work with cached data', () async {
      // Test that category dialogs can be displayed using cached data when offline
      
      final contactUserId = 'offline-contact-123';
      final callerPhone = '+5555555555';
      final contactPhoneNumbers = ['+4444444444'];
      
      final testCategory = models.Category(
        id: 'test-category-offline',
        userId: contactUserId,
        type: models.CategoryType.coWorkers,
        note: 'Test offline category',
        timeSlots: [
          models.TimeSlot(
            id: 'timeslot-1',
            categoryId: 'test-category-offline',
            dayOfWeek: 1, // Monday
            startTime: '09:00',
            endTime: '17:00',
            createdAt: DateTime.now(),
          ),
        ],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Simulate having cached data (as if it was received via real-time updates)
      offlineService.updateContactAssignment(callerPhone, testCategory);

      // Test that the category can be retrieved for dialog display
      final result = await offlineService.getContactCategoryWithPhones(
        contactUserId: contactUserId,
        callerPhone: callerPhone,
        contactPhoneNumbers: contactPhoneNumbers,
      );

      expect(result, isNotNull);
      expect(result?.id, equals(testCategory.id));
      expect(result?.type, equals(models.CategoryType.coWorkers));
      expect(result?.timeSlots.length, equals(1));
      expect(result?.timeSlots.first.startTime, equals('09:00'));
      expect(result?.timeSlots.first.endTime, equals('17:00'));
    });
  });
}
