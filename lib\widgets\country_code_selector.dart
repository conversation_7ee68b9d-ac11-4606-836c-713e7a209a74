import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';

class CountryCode {
  final String nameEn;
  final String nameAr;
  final String code;
  final String dialCode;
  final String flag;

  const CountryCode({
    required this.nameEn,
    required this.nameAr,
    required this.code,
    required this.dialCode,
    required this.flag,
  });

  String getName(String languageCode) {
    return languageCode == 'ar' ? nameAr : nameEn;
  }
}

class CountryCodeSelector extends StatefulWidget {
  final String selectedCountryCode;
  final Function(CountryCode) onCountrySelected;

  const CountryCodeSelector({
    Key? key,
    required this.selectedCountryCode,
    required this.onCountrySelected,
  }) : super(key: key);

  @override
  State<CountryCodeSelector> createState() => _CountryCodeSelectorState();
}

class _CountryCodeSelectorState extends State<CountryCodeSelector> {
  final TextEditingController _searchController = TextEditingController();
  List<CountryCode> _filteredCountries = [];

  static const List<CountryCode> _countries = [
    CountryCode(
        nameEn: 'Yemen',
        nameAr: 'اليمن',
        code: 'YE',
        dialCode: '+967',
        flag: '🇾🇪'),
    CountryCode(
        nameEn: 'Saudi Arabia',
        nameAr: 'السعودية',
        code: 'SA',
        dialCode: '+966',
        flag: '🇸🇦'),
    CountryCode(
        nameEn: 'United Arab Emirates',
        nameAr: 'الإمارات العربية المتحدة',
        code: 'AE',
        dialCode: '+971',
        flag: '🇦🇪'),
    CountryCode(
        nameEn: 'Egypt',
        nameAr: 'مصر',
        code: 'EG',
        dialCode: '+20',
        flag: '🇪🇬'),
    CountryCode(
        nameEn: 'Jordan',
        nameAr: 'الأردن',
        code: 'JO',
        dialCode: '+962',
        flag: '🇯🇴'),
    CountryCode(
        nameEn: 'Lebanon',
        nameAr: 'لبنان',
        code: 'LB',
        dialCode: '+961',
        flag: '🇱🇧'),
    CountryCode(
        nameEn: 'Syria',
        nameAr: 'سوريا',
        code: 'SY',
        dialCode: '+963',
        flag: '🇸🇾'),
    CountryCode(
        nameEn: 'Iraq',
        nameAr: 'العراق',
        code: 'IQ',
        dialCode: '+964',
        flag: '🇮🇶'),
    CountryCode(
        nameEn: 'Kuwait',
        nameAr: 'الكويت',
        code: 'KW',
        dialCode: '+965',
        flag: '🇰🇼'),
    CountryCode(
        nameEn: 'Qatar',
        nameAr: 'قطر',
        code: 'QA',
        dialCode: '+974',
        flag: '🇶🇦'),
    CountryCode(
        nameEn: 'Bahrain',
        nameAr: 'البحرين',
        code: 'BH',
        dialCode: '+973',
        flag: '🇧🇭'),
    CountryCode(
        nameEn: 'Oman',
        nameAr: 'عُمان',
        code: 'OM',
        dialCode: '+968',
        flag: '🇴🇲'),
    CountryCode(
        nameEn: 'Palestine',
        nameAr: 'فلسطين',
        code: 'PS',
        dialCode: '+970',
        flag: '🇵🇸'),
    CountryCode(
        nameEn: 'Morocco',
        nameAr: 'المغرب',
        code: 'MA',
        dialCode: '+212',
        flag: '🇲🇦'),
    CountryCode(
        nameEn: 'Algeria',
        nameAr: 'الجزائر',
        code: 'DZ',
        dialCode: '+213',
        flag: '🇩🇿'),
    CountryCode(
        nameEn: 'Tunisia',
        nameAr: 'تونس',
        code: 'TN',
        dialCode: '+216',
        flag: '🇹🇳'),
    CountryCode(
        nameEn: 'Libya',
        nameAr: 'ليبيا',
        code: 'LY',
        dialCode: '+218',
        flag: '🇱🇾'),
    CountryCode(
        nameEn: 'Sudan',
        nameAr: 'السودان',
        code: 'SD',
        dialCode: '+249',
        flag: '🇸🇩'),
    CountryCode(
        nameEn: 'Somalia',
        nameAr: 'الصومال',
        code: 'SO',
        dialCode: '+252',
        flag: '🇸🇴'),
    CountryCode(
        nameEn: 'Djibouti',
        nameAr: 'جيبوتي',
        code: 'DJ',
        dialCode: '+253',
        flag: '🇩🇯'),
    CountryCode(
        nameEn: 'Comoros',
        nameAr: 'جزر القمر',
        code: 'KM',
        dialCode: '+269',
        flag: '🇰🇲'),
    CountryCode(
        nameEn: 'Mauritania',
        nameAr: 'موريتانيا',
        code: 'MR',
        dialCode: '+222',
        flag: '🇲🇷'),
    CountryCode(
        nameEn: 'United States',
        nameAr: 'الولايات المتحدة',
        code: 'US',
        dialCode: '+1',
        flag: '🇺🇸'),
    CountryCode(
        nameEn: 'United Kingdom',
        nameAr: 'المملكة المتحدة',
        code: 'GB',
        dialCode: '+44',
        flag: '🇬🇧'),
    CountryCode(
        nameEn: 'Canada',
        nameAr: 'كندا',
        code: 'CA',
        dialCode: '+1',
        flag: '🇨🇦'),
    CountryCode(
        nameEn: 'Australia',
        nameAr: 'أستراليا',
        code: 'AU',
        dialCode: '+61',
        flag: '🇦🇺'),
    CountryCode(
        nameEn: 'Germany',
        nameAr: 'ألمانيا',
        code: 'DE',
        dialCode: '+49',
        flag: '🇩🇪'),
    CountryCode(
        nameEn: 'France',
        nameAr: 'فرنسا',
        code: 'FR',
        dialCode: '+33',
        flag: '🇫🇷'),
    CountryCode(
        nameEn: 'Italy',
        nameAr: 'إيطاليا',
        code: 'IT',
        dialCode: '+39',
        flag: '🇮🇹'),
    CountryCode(
        nameEn: 'Spain',
        nameAr: 'إسبانيا',
        code: 'ES',
        dialCode: '+34',
        flag: '🇪🇸'),
    CountryCode(
        nameEn: 'Netherlands',
        nameAr: 'هولندا',
        code: 'NL',
        dialCode: '+31',
        flag: '🇳🇱'),
    CountryCode(
        nameEn: 'Belgium',
        nameAr: 'بلجيكا',
        code: 'BE',
        dialCode: '+32',
        flag: '🇧🇪'),
    CountryCode(
        nameEn: 'Switzerland',
        nameAr: 'سويسرا',
        code: 'CH',
        dialCode: '+41',
        flag: '🇨🇭'),
    CountryCode(
        nameEn: 'Austria',
        nameAr: 'النمسا',
        code: 'AT',
        dialCode: '+43',
        flag: '🇦🇹'),
    CountryCode(
        nameEn: 'Sweden',
        nameAr: 'السويد',
        code: 'SE',
        dialCode: '+46',
        flag: '🇸🇪'),
    CountryCode(
        nameEn: 'Norway',
        nameAr: 'النرويج',
        code: 'NO',
        dialCode: '+47',
        flag: '🇳🇴'),
    CountryCode(
        nameEn: 'Denmark',
        nameAr: 'الدنمارك',
        code: 'DK',
        dialCode: '+45',
        flag: '🇩🇰'),
    CountryCode(
        nameEn: 'Finland',
        nameAr: 'فنلندا',
        code: 'FI',
        dialCode: '+358',
        flag: '🇫🇮'),
    CountryCode(
        nameEn: 'Poland',
        nameAr: 'بولندا',
        code: 'PL',
        dialCode: '+48',
        flag: '🇵🇱'),
    CountryCode(
        nameEn: 'Czech Republic',
        nameAr: 'التشيك',
        code: 'CZ',
        dialCode: '+420',
        flag: '🇨🇿'),
    CountryCode(
        nameEn: 'Hungary',
        nameAr: 'المجر',
        code: 'HU',
        dialCode: '+36',
        flag: '🇭🇺'),
    CountryCode(
        nameEn: 'Romania',
        nameAr: 'رومانيا',
        code: 'RO',
        dialCode: '+40',
        flag: '🇷🇴'),
    CountryCode(
        nameEn: 'Bulgaria',
        nameAr: 'بلغاريا',
        code: 'BG',
        dialCode: '+359',
        flag: '🇧🇬'),
    CountryCode(
        nameEn: 'Greece',
        nameAr: 'اليونان',
        code: 'GR',
        dialCode: '+30',
        flag: '🇬🇷'),
    CountryCode(
        nameEn: 'Turkey',
        nameAr: 'تركيا',
        code: 'TR',
        dialCode: '+90',
        flag: '🇹🇷'),
    CountryCode(
        nameEn: 'Russia',
        nameAr: 'روسيا',
        code: 'RU',
        dialCode: '+7',
        flag: '🇷🇺'),
    CountryCode(
        nameEn: 'China',
        nameAr: 'الصين',
        code: 'CN',
        dialCode: '+86',
        flag: '🇨🇳'),
    CountryCode(
        nameEn: 'Japan',
        nameAr: 'اليابان',
        code: 'JP',
        dialCode: '+81',
        flag: '🇯🇵'),
    CountryCode(
        nameEn: 'India',
        nameAr: 'الهند',
        code: 'IN',
        dialCode: '+91',
        flag: '🇮🇳'),
    CountryCode(
        nameEn: 'Pakistan',
        nameAr: 'باكستان',
        code: 'PK',
        dialCode: '+92',
        flag: '🇵🇰'),
    CountryCode(
        nameEn: 'Afghanistan',
        nameAr: 'أفغانستان',
        code: 'AF',
        dialCode: '+93',
        flag: '🇦🇫'),
    CountryCode(
        nameEn: 'Iran',
        nameAr: 'إيران',
        code: 'IR',
        dialCode: '+98',
        flag: '🇮🇷'),
    // Additional major countries with basic English names for now
    CountryCode(
        nameEn: 'Brazil',
        nameAr: 'البرازيل',
        code: 'BR',
        dialCode: '+55',
        flag: '🇧🇷'),
    CountryCode(
        nameEn: 'Mexico',
        nameAr: 'المكسيك',
        code: 'MX',
        dialCode: '+52',
        flag: '🇲🇽'),
    CountryCode(
        nameEn: 'South Africa',
        nameAr: 'جنوب أفريقيا',
        code: 'ZA',
        dialCode: '+27',
        flag: '🇿🇦'),
  ];

  @override
  void initState() {
    super.initState();
    _filteredCountries = _countries;
    _searchController.addListener(_filterCountries);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterCountries() {
    final query = _searchController.text.toLowerCase();
    if (mounted) {
      setState(() {
        _filteredCountries = _countries.where((country) {
          return country.nameEn.toLowerCase().contains(query) ||
              country.nameAr.toLowerCase().contains(query) ||
              country.dialCode.contains(query) ||
              country.code.toLowerCase().contains(query);
        }).toList();
      });
    }
  }

  CountryCode get _selectedCountry {
    return _countries.firstWhere(
      (country) => country.dialCode == widget.selectedCountryCode,
      orElse: () => _countries.first,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _showCountryPicker,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _selectedCountry.flag,
              style: const TextStyle(fontSize: 20),
            ),
            const SizedBox(width: 8),
            Text(
              _selectedCountry.dialCode,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.keyboard_arrow_down_rounded,
              color: Colors.white.withOpacity(0.7),
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  void _showCountryPicker() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildCountryPickerModal(),
    );
  }

  Widget _buildCountryPickerModal() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    AppLocalizations.of(context)?.selectCountry ??
                        'Select Country',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close_rounded),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.grey[100],
                    foregroundColor: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          // Search field
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: AppLocalizations.of(context)?.searchCountries ??
                    'Search countries...',
                prefixIcon: const Icon(Icons.search_rounded),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16),
                  borderSide:
                      const BorderSide(color: Color(0xFF6366F1), width: 2),
                ),
                filled: true,
                fillColor: Colors.grey[50],
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Countries list
          Expanded(
            child: ListView.builder(
              itemCount: _filteredCountries.length,
              itemBuilder: (context, index) {
                final country = _filteredCountries[index];
                final isSelected =
                    country.dialCode == widget.selectedCountryCode;

                return ListTile(
                  leading: Text(
                    country.flag,
                    style: const TextStyle(fontSize: 24),
                  ),
                  title: Text(
                    country
                        .getName(Localizations.localeOf(context).languageCode),
                    style: TextStyle(
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                      color:
                          isSelected ? const Color(0xFF6366F1) : Colors.black87,
                    ),
                  ),
                  trailing: Text(
                    country.dialCode,
                    style: TextStyle(
                      color: isSelected
                          ? const Color(0xFF6366F1)
                          : Colors.grey[600],
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                  selected: isSelected,
                  selectedTileColor: const Color(0xFF6366F1).withOpacity(0.1),
                  onTap: () {
                    widget.onCountrySelected(country);
                    Navigator.pop(context);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
