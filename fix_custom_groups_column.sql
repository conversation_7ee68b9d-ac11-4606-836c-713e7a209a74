-- Fix for custom_groups column in profiles table
-- This script adds the missing custom_groups column if it doesn't exist

DO $$
BEGIN
    -- Check if custom_groups column exists, if not add it
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'profiles'
        AND column_name = 'custom_groups'
    ) THEN
        ALTER TABLE public.profiles
        ADD COLUMN custom_groups JSONB DEFAULT '[]'::jsonb;

        RAISE NOTICE 'Added custom_groups column to profiles table';
    ELSE
        RAISE NOTICE 'custom_groups column already exists in profiles table';
    END IF;
END $$;

-- Verify the column was added
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_schema = 'public'
AND table_name = 'profiles'
AND column_name IN ('exception_groups', 'custom_groups')
ORDER BY column_name;
