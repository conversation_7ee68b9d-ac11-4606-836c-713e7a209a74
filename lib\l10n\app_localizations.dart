import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en')
  ];

  /// The title of the application
  ///
  /// In en, this message translates to:
  /// **'Contact Times'**
  String get appTitle;

  /// The subtitle of the application
  ///
  /// In en, this message translates to:
  /// **'Manage when you can be contacted'**
  String get appSubtitle;

  /// No description provided for @signIn.
  ///
  /// In en, this message translates to:
  /// **'Sign In'**
  String get signIn;

  /// No description provided for @signUp.
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signUp;

  /// No description provided for @signOut.
  ///
  /// In en, this message translates to:
  /// **'Sign Out'**
  String get signOut;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @confirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPassword;

  /// No description provided for @fullName.
  ///
  /// In en, this message translates to:
  /// **'Full Name'**
  String get fullName;

  /// No description provided for @phoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phoneNumber;

  /// No description provided for @username.
  ///
  /// In en, this message translates to:
  /// **'Username'**
  String get username;

  /// No description provided for @pleaseEnterEmail.
  ///
  /// In en, this message translates to:
  /// **'Please enter your email'**
  String get pleaseEnterEmail;

  /// No description provided for @pleaseEnterValidEmail.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email'**
  String get pleaseEnterValidEmail;

  /// No description provided for @pleaseEnterPassword.
  ///
  /// In en, this message translates to:
  /// **'Please enter your password'**
  String get pleaseEnterPassword;

  /// No description provided for @pleaseEnterFullName.
  ///
  /// In en, this message translates to:
  /// **'Please enter your full name'**
  String get pleaseEnterFullName;

  /// No description provided for @pleaseEnterPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Please enter your phone number'**
  String get pleaseEnterPhoneNumber;

  /// No description provided for @pleaseEnterUsername.
  ///
  /// In en, this message translates to:
  /// **'Please enter your username'**
  String get pleaseEnterUsername;

  /// No description provided for @passwordsDoNotMatch.
  ///
  /// In en, this message translates to:
  /// **'Passwords do not match'**
  String get passwordsDoNotMatch;

  /// No description provided for @pleaseConfirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Please confirm your password'**
  String get pleaseConfirmPassword;

  /// No description provided for @passwordMustBeAtLeast6Characters.
  ///
  /// In en, this message translates to:
  /// **'Password must be at least 6 characters'**
  String get passwordMustBeAtLeast6Characters;

  /// No description provided for @pleaseEnterValidPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid phone number'**
  String get pleaseEnterValidPhoneNumber;

  /// No description provided for @createAccount.
  ///
  /// In en, this message translates to:
  /// **'Create Account'**
  String get createAccount;

  /// No description provided for @joinContactTimesToday.
  ///
  /// In en, this message translates to:
  /// **'Join Contact Times today'**
  String get joinContactTimesToday;

  /// No description provided for @phoneNumberAlreadyRegistered.
  ///
  /// In en, this message translates to:
  /// **'Phone Number Already Registered'**
  String get phoneNumberAlreadyRegistered;

  /// No description provided for @thisPhoneNumberIsAlreadyRegistered.
  ///
  /// In en, this message translates to:
  /// **'This phone number is already associated with an account. Would you like to sign in instead?'**
  String get thisPhoneNumberIsAlreadyRegistered;

  /// No description provided for @emailAlreadyRegistered.
  ///
  /// In en, this message translates to:
  /// **'Email Already Registered'**
  String get emailAlreadyRegistered;

  /// No description provided for @thisEmailIsAlreadyRegistered.
  ///
  /// In en, this message translates to:
  /// **'This email is already associated with an account. Would you like to sign in instead?'**
  String get thisEmailIsAlreadyRegistered;

  /// No description provided for @invalidEmail.
  ///
  /// In en, this message translates to:
  /// **'Invalid Email'**
  String get invalidEmail;

  /// No description provided for @weakPassword.
  ///
  /// In en, this message translates to:
  /// **'Weak Password'**
  String get weakPassword;

  /// No description provided for @pleaseChooseStrongerPassword.
  ///
  /// In en, this message translates to:
  /// **'Please choose a stronger password with at least 6 characters'**
  String get pleaseChooseStrongerPassword;

  /// No description provided for @accountSetupFailed.
  ///
  /// In en, this message translates to:
  /// **'Account Setup Failed'**
  String get accountSetupFailed;

  /// No description provided for @accountCreatedButProfileSetupFailed.
  ///
  /// In en, this message translates to:
  /// **'Your account was created but we couldn\'t set up your profile. Please contact support'**
  String get accountCreatedButProfileSetupFailed;

  /// No description provided for @signUpFailedTitle.
  ///
  /// In en, this message translates to:
  /// **'Sign Up Failed'**
  String get signUpFailedTitle;

  /// No description provided for @unexpectedErrorOccurred.
  ///
  /// In en, this message translates to:
  /// **'An unexpected error occurred. Please try again'**
  String get unexpectedErrorOccurred;

  /// No description provided for @signInFailed.
  ///
  /// In en, this message translates to:
  /// **'Sign in failed'**
  String get signInFailed;

  /// No description provided for @signUpFailed.
  ///
  /// In en, this message translates to:
  /// **'Sign up failed'**
  String get signUpFailed;

  /// No description provided for @profileUpdateFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to update profile'**
  String get profileUpdateFailed;

  /// No description provided for @profileUpdatedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Profile updated successfully'**
  String get profileUpdatedSuccessfully;

  /// No description provided for @dontHaveAccount.
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account? Sign Up'**
  String get dontHaveAccount;

  /// No description provided for @alreadyHaveAccount.
  ///
  /// In en, this message translates to:
  /// **'Already have an account? Sign In'**
  String get alreadyHaveAccount;

  /// No description provided for @contacts.
  ///
  /// In en, this message translates to:
  /// **'Contacts'**
  String get contacts;

  /// No description provided for @categories.
  ///
  /// In en, this message translates to:
  /// **'Categories'**
  String get categories;

  /// No description provided for @distribute.
  ///
  /// In en, this message translates to:
  /// **'Distribute'**
  String get distribute;

  /// No description provided for @profile.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// No description provided for @dashboard.
  ///
  /// In en, this message translates to:
  /// **'Dashboard'**
  String get dashboard;

  /// No description provided for @loadingContacts.
  ///
  /// In en, this message translates to:
  /// **'Loading contacts...'**
  String get loadingContacts;

  /// No description provided for @noContactsFound.
  ///
  /// In en, this message translates to:
  /// **'No contacts found'**
  String get noContactsFound;

  /// No description provided for @contactsPermissionRequired.
  ///
  /// In en, this message translates to:
  /// **'Contacts permission is required to sync your contacts'**
  String get contactsPermissionRequired;

  /// No description provided for @grantPermission.
  ///
  /// In en, this message translates to:
  /// **'Grant Permission'**
  String get grantPermission;

  /// No description provided for @syncContacts.
  ///
  /// In en, this message translates to:
  /// **'Sync Contacts'**
  String get syncContacts;

  /// No description provided for @availableTimes.
  ///
  /// In en, this message translates to:
  /// **'Available Times'**
  String get availableTimes;

  /// No description provided for @timeSlots.
  ///
  /// In en, this message translates to:
  /// **'Time Slots'**
  String get timeSlots;

  /// No description provided for @addTimeSlot.
  ///
  /// In en, this message translates to:
  /// **'Add Time Slot'**
  String get addTimeSlot;

  /// No description provided for @editTimeSlots.
  ///
  /// In en, this message translates to:
  /// **'Edit Time Slots'**
  String get editTimeSlots;

  /// No description provided for @saveTimeSlots.
  ///
  /// In en, this message translates to:
  /// **'Save Time Slots'**
  String get saveTimeSlots;

  /// No description provided for @startTime.
  ///
  /// In en, this message translates to:
  /// **'Start Time'**
  String get startTime;

  /// No description provided for @endTime.
  ///
  /// In en, this message translates to:
  /// **'End Time'**
  String get endTime;

  /// No description provided for @selectDay.
  ///
  /// In en, this message translates to:
  /// **'Select Day'**
  String get selectDay;

  /// No description provided for @monday.
  ///
  /// In en, this message translates to:
  /// **'Monday'**
  String get monday;

  /// No description provided for @tuesday.
  ///
  /// In en, this message translates to:
  /// **'Tuesday'**
  String get tuesday;

  /// No description provided for @wednesday.
  ///
  /// In en, this message translates to:
  /// **'Wednesday'**
  String get wednesday;

  /// No description provided for @thursday.
  ///
  /// In en, this message translates to:
  /// **'Thursday'**
  String get thursday;

  /// No description provided for @friday.
  ///
  /// In en, this message translates to:
  /// **'Friday'**
  String get friday;

  /// No description provided for @saturday.
  ///
  /// In en, this message translates to:
  /// **'Saturday'**
  String get saturday;

  /// No description provided for @sunday.
  ///
  /// In en, this message translates to:
  /// **'Sunday'**
  String get sunday;

  /// No description provided for @activeNow.
  ///
  /// In en, this message translates to:
  /// **'ACTIVE NOW'**
  String get activeNow;

  /// No description provided for @notify.
  ///
  /// In en, this message translates to:
  /// **'Notify'**
  String get notify;

  /// No description provided for @call.
  ///
  /// In en, this message translates to:
  /// **'Call'**
  String get call;

  /// No description provided for @message.
  ///
  /// In en, this message translates to:
  /// **'Message'**
  String get message;

  /// No description provided for @profileInformation.
  ///
  /// In en, this message translates to:
  /// **'Profile Information'**
  String get profileInformation;

  /// No description provided for @appInformation.
  ///
  /// In en, this message translates to:
  /// **'App Information'**
  String get appInformation;

  /// No description provided for @version.
  ///
  /// In en, this message translates to:
  /// **'Version'**
  String get version;

  /// No description provided for @buildNumber.
  ///
  /// In en, this message translates to:
  /// **'Build Number'**
  String get buildNumber;

  /// No description provided for @notificationSettings.
  ///
  /// In en, this message translates to:
  /// **'Notification Settings'**
  String get notificationSettings;

  /// No description provided for @changePhoto.
  ///
  /// In en, this message translates to:
  /// **'Change Photo'**
  String get changePhoto;

  /// No description provided for @updateProfile.
  ///
  /// In en, this message translates to:
  /// **'Update Profile'**
  String get updateProfile;

  /// No description provided for @aboutContactTimes.
  ///
  /// In en, this message translates to:
  /// **'About Contact Times'**
  String get aboutContactTimes;

  /// No description provided for @manageWhenYouCanBeContacted.
  ///
  /// In en, this message translates to:
  /// **'Manage when you can be contacted by different people. Set up categories and time slots to let others know your availability preferences.'**
  String get manageWhenYouCanBeContacted;

  /// No description provided for @loadingProfile.
  ///
  /// In en, this message translates to:
  /// **'Loading Profile...'**
  String get loadingProfile;

  /// No description provided for @profileNotFound.
  ///
  /// In en, this message translates to:
  /// **'Profile not found'**
  String get profileNotFound;

  /// No description provided for @profilePictureUploadComingSoon.
  ///
  /// In en, this message translates to:
  /// **'Profile picture upload coming soon'**
  String get profilePictureUploadComingSoon;

  /// No description provided for @noPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'No phone number'**
  String get noPhoneNumber;

  /// No description provided for @welcome.
  ///
  /// In en, this message translates to:
  /// **'Welcome!'**
  String get welcome;

  /// No description provided for @failedToSignOut.
  ///
  /// In en, this message translates to:
  /// **'Failed to sign out'**
  String get failedToSignOut;

  /// No description provided for @uploadProfilePicture.
  ///
  /// In en, this message translates to:
  /// **'Upload Profile Picture'**
  String get uploadProfilePicture;

  /// No description provided for @selectImageSource.
  ///
  /// In en, this message translates to:
  /// **'Select Image Source'**
  String get selectImageSource;

  /// No description provided for @camera.
  ///
  /// In en, this message translates to:
  /// **'Camera'**
  String get camera;

  /// No description provided for @gallery.
  ///
  /// In en, this message translates to:
  /// **'Gallery'**
  String get gallery;

  /// No description provided for @imageUploadedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Profile picture updated successfully'**
  String get imageUploadedSuccessfully;

  /// No description provided for @failedToUploadImage.
  ///
  /// In en, this message translates to:
  /// **'Failed to upload image'**
  String get failedToUploadImage;

  /// No description provided for @deleteAccount.
  ///
  /// In en, this message translates to:
  /// **'Delete Account'**
  String get deleteAccount;

  /// No description provided for @deleteAccountConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete your account? This action cannot be undone.'**
  String get deleteAccountConfirmation;

  /// No description provided for @deleteAccountWarning.
  ///
  /// In en, this message translates to:
  /// **'This will permanently delete your account and all associated data.'**
  String get deleteAccountWarning;

  /// No description provided for @confirmDelete.
  ///
  /// In en, this message translates to:
  /// **'Confirm Delete'**
  String get confirmDelete;

  /// No description provided for @accountDeletedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Account deleted successfully'**
  String get accountDeletedSuccessfully;

  /// No description provided for @failedToDeleteAccount.
  ///
  /// In en, this message translates to:
  /// **'Failed to delete account'**
  String get failedToDeleteAccount;

  /// No description provided for @enterYourFullName.
  ///
  /// In en, this message translates to:
  /// **'Enter your full name'**
  String get enterYourFullName;

  /// No description provided for @enterYourUsername.
  ///
  /// In en, this message translates to:
  /// **'Enter your username'**
  String get enterYourUsername;

  /// No description provided for @enterYourPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Enter your phone number'**
  String get enterYourPhoneNumber;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @selectLanguage.
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectLanguage;

  /// No description provided for @english.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// No description provided for @arabic.
  ///
  /// In en, this message translates to:
  /// **'العربية'**
  String get arabic;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @edit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// No description provided for @done.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// No description provided for @back.
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back;

  /// No description provided for @next.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// No description provided for @previous.
  ///
  /// In en, this message translates to:
  /// **'Previous'**
  String get previous;

  /// No description provided for @close.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// No description provided for @error.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// No description provided for @success.
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// No description provided for @warning.
  ///
  /// In en, this message translates to:
  /// **'Warning'**
  String get warning;

  /// No description provided for @info.
  ///
  /// In en, this message translates to:
  /// **'Info'**
  String get info;

  /// No description provided for @offline.
  ///
  /// In en, this message translates to:
  /// **'Offline'**
  String get offline;

  /// No description provided for @online.
  ///
  /// In en, this message translates to:
  /// **'Online'**
  String get online;

  /// No description provided for @syncing.
  ///
  /// In en, this message translates to:
  /// **'Syncing...'**
  String get syncing;

  /// No description provided for @syncComplete.
  ///
  /// In en, this message translates to:
  /// **'Sync Complete'**
  String get syncComplete;

  /// No description provided for @noTimeSlots.
  ///
  /// In en, this message translates to:
  /// **'No time slots configured'**
  String get noTimeSlots;

  /// No description provided for @addFirstTimeSlot.
  ///
  /// In en, this message translates to:
  /// **'Add your first time slot'**
  String get addFirstTimeSlot;

  /// No description provided for @timeSlotOverlap.
  ///
  /// In en, this message translates to:
  /// **'Time slots overlap and will be merged'**
  String get timeSlotOverlap;

  /// No description provided for @categoryAssigned.
  ///
  /// In en, this message translates to:
  /// **'Category assigned'**
  String get categoryAssigned;

  /// No description provided for @categoryUpdated.
  ///
  /// In en, this message translates to:
  /// **'Category updated'**
  String get categoryUpdated;

  /// No description provided for @contactCategorized.
  ///
  /// In en, this message translates to:
  /// **'Contact categorized successfully'**
  String get contactCategorized;

  /// No description provided for @loading.
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// No description provided for @retry.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// No description provided for @refresh.
  ///
  /// In en, this message translates to:
  /// **'Refresh'**
  String get refresh;

  /// No description provided for @myCategories.
  ///
  /// In en, this message translates to:
  /// **'My Categories'**
  String get myCategories;

  /// No description provided for @pending.
  ///
  /// In en, this message translates to:
  /// **'Pending'**
  String get pending;

  /// No description provided for @loadingYourCategories.
  ///
  /// In en, this message translates to:
  /// **'Loading your categories...'**
  String get loadingYourCategories;

  /// No description provided for @oopsSomethingWentWrong.
  ///
  /// In en, this message translates to:
  /// **'Oops! Something went wrong'**
  String get oopsSomethingWentWrong;

  /// No description provided for @tryAgain.
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get tryAgain;

  /// No description provided for @categoryNoteUpdatedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Category note updated successfully'**
  String get categoryNoteUpdatedSuccessfully;

  /// No description provided for @updatedLocally.
  ///
  /// In en, this message translates to:
  /// **'Updated locally. Will sync when online.'**
  String get updatedLocally;

  /// No description provided for @updatedOffline.
  ///
  /// In en, this message translates to:
  /// **'Updated offline. Will sync when online.'**
  String get updatedOffline;

  /// No description provided for @failedToUpdateCategory.
  ///
  /// In en, this message translates to:
  /// **'Failed to update category'**
  String get failedToUpdateCategory;

  /// No description provided for @timeSlotsUpdatedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Time slots updated successfully'**
  String get timeSlotsUpdatedSuccessfully;

  /// No description provided for @allChangesSyncedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'All changes synced successfully'**
  String get allChangesSyncedSuccessfully;

  /// No description provided for @noDescriptionAddedYet.
  ///
  /// In en, this message translates to:
  /// **'No description added yet...'**
  String get noDescriptionAddedYet;

  /// No description provided for @todaysTimeSlots.
  ///
  /// In en, this message translates to:
  /// **'Today\'s Time Slots ({count})'**
  String todaysTimeSlots(int count);

  /// No description provided for @timeSlotsCount.
  ///
  /// In en, this message translates to:
  /// **'Time Slots ({count})'**
  String timeSlotsCount(int count);

  /// No description provided for @perfectTimeToContact.
  ///
  /// In en, this message translates to:
  /// **'Perfect time to contact!'**
  String get perfectTimeToContact;

  /// No description provided for @andMoreSlotsToday.
  ///
  /// In en, this message translates to:
  /// **'... and {count} more slots today'**
  String andMoreSlotsToday(int count);

  /// No description provided for @noTimeSlotsForToday.
  ///
  /// In en, this message translates to:
  /// **'No time slots for today. You have {count} slot{plural} on other days.'**
  String noTimeSlotsForToday(int count, String plural);

  /// No description provided for @noTimeSlotsConfiguredYet.
  ///
  /// In en, this message translates to:
  /// **'No time slots configured yet'**
  String get noTimeSlotsConfiguredYet;

  /// No description provided for @editCategory.
  ///
  /// In en, this message translates to:
  /// **'Edit {categoryName}'**
  String editCategory(String categoryName);

  /// No description provided for @description.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// No description provided for @addDescriptionForCategory.
  ///
  /// In en, this message translates to:
  /// **'Add a description for this category...'**
  String get addDescriptionForCategory;

  /// No description provided for @timeSlotNumber.
  ///
  /// In en, this message translates to:
  /// **'Time Slot {number}'**
  String timeSlotNumber(int number);

  /// No description provided for @dayOfWeek.
  ///
  /// In en, this message translates to:
  /// **'Day of Week'**
  String get dayOfWeek;

  /// No description provided for @noTimeSlotsYet.
  ///
  /// In en, this message translates to:
  /// **'No Time Slots Yet'**
  String get noTimeSlotsYet;

  /// No description provided for @addYourFirstTimeSlot.
  ///
  /// In en, this message translates to:
  /// **'Add your first time slot to get started.\nLet others know when you\'re available!'**
  String get addYourFirstTimeSlot;

  /// No description provided for @addYourFirstTimeSlotButton.
  ///
  /// In en, this message translates to:
  /// **'Add Your First Time Slot'**
  String get addYourFirstTimeSlotButton;

  /// No description provided for @timeSlotOverlapWarning.
  ///
  /// In en, this message translates to:
  /// **'This slot overlaps with others and will be merged when saved'**
  String get timeSlotOverlapWarning;

  /// No description provided for @saving.
  ///
  /// In en, this message translates to:
  /// **'Saving...'**
  String get saving;

  /// No description provided for @saveChanges.
  ///
  /// In en, this message translates to:
  /// **'Save Changes'**
  String get saveChanges;

  /// No description provided for @pleaseAddAtLeastOneTimeSlot.
  ///
  /// In en, this message translates to:
  /// **'Please add at least one time slot'**
  String get pleaseAddAtLeastOneTimeSlot;

  /// No description provided for @mergedOverlappingTimeSlots.
  ///
  /// In en, this message translates to:
  /// **'Merged {count} overlapping time slot{plural}'**
  String mergedOverlappingTimeSlots(int count, String plural);

  /// No description provided for @failedToSaveTimeSlots.
  ///
  /// In en, this message translates to:
  /// **'Failed to save time slots: {error}'**
  String failedToSaveTimeSlots(String error);

  /// No description provided for @timeSlotsCountSimple.
  ///
  /// In en, this message translates to:
  /// **'{count} time slot{plural}'**
  String timeSlotsCountSimple(int count, String plural);

  /// No description provided for @hint.
  ///
  /// In en, this message translates to:
  /// **'Hint'**
  String get hint;

  /// No description provided for @enterYourFullNameHint.
  ///
  /// In en, this message translates to:
  /// **'Enter your full name'**
  String get enterYourFullNameHint;

  /// No description provided for @enterYourUsernameHint.
  ///
  /// In en, this message translates to:
  /// **'Enter your username'**
  String get enterYourUsernameHint;

  /// No description provided for @enterYourPhoneNumberHint.
  ///
  /// In en, this message translates to:
  /// **'Enter your phone number'**
  String get enterYourPhoneNumberHint;

  /// No description provided for @contactsPermissionDenied.
  ///
  /// In en, this message translates to:
  /// **'Contacts permission denied'**
  String get contactsPermissionDenied;

  /// No description provided for @deviceContactsAccessRequired.
  ///
  /// In en, this message translates to:
  /// **'Device contacts access required'**
  String get deviceContactsAccessRequired;

  /// No description provided for @deviceContactsAccessDescription.
  ///
  /// In en, this message translates to:
  /// **'The app needs access to your device contacts to show people using Contact Times and sync contact information'**
  String get deviceContactsAccessDescription;

  /// No description provided for @allowContactsAccess.
  ///
  /// In en, this message translates to:
  /// **'Allow contacts access'**
  String get allowContactsAccess;

  /// No description provided for @contactsSynced.
  ///
  /// In en, this message translates to:
  /// **'Contacts synced successfully'**
  String get contactsSynced;

  /// No description provided for @failedToSyncContacts.
  ///
  /// In en, this message translates to:
  /// **'Failed to sync contacts'**
  String get failedToSyncContacts;

  /// Placeholder text for contact search input field
  ///
  /// In en, this message translates to:
  /// **'Search contacts...'**
  String get searchContacts;

  /// Message shown when no contacts are available for alphabet navigation
  ///
  /// In en, this message translates to:
  /// **'No contacts available'**
  String get noContactsAvailable;

  /// No description provided for @tapToAssignCategory.
  ///
  /// In en, this message translates to:
  /// **'Tap to assign category'**
  String get tapToAssignCategory;

  /// No description provided for @categoryAssignedTo.
  ///
  /// In en, this message translates to:
  /// **'Category assigned to {contactName}'**
  String categoryAssignedTo(String contactName);

  /// No description provided for @timeSlot.
  ///
  /// In en, this message translates to:
  /// **'Time Slot'**
  String get timeSlot;

  /// No description provided for @noTimeSlotsConfigured.
  ///
  /// In en, this message translates to:
  /// **'No time slots configured'**
  String get noTimeSlotsConfigured;

  /// No description provided for @overlappingTimeSlots.
  ///
  /// In en, this message translates to:
  /// **'Overlapping time slots will be merged'**
  String get overlappingTimeSlots;

  /// No description provided for @invalidTimeSlot.
  ///
  /// In en, this message translates to:
  /// **'Invalid time slot'**
  String get invalidTimeSlot;

  /// No description provided for @startTimeMustBeBeforeEndTime.
  ///
  /// In en, this message translates to:
  /// **'Start time must be before end time'**
  String get startTimeMustBeBeforeEndTime;

  /// No description provided for @invalidTimeSlotRange.
  ///
  /// In en, this message translates to:
  /// **'Invalid time range: Start time ({startTime}) must be before end time ({endTime})'**
  String invalidTimeSlotRange(String startTime, String endTime);

  /// No description provided for @distributeContacts.
  ///
  /// In en, this message translates to:
  /// **'Distribute Contacts'**
  String get distributeContacts;

  /// No description provided for @assignContactsToCategories.
  ///
  /// In en, this message translates to:
  /// **'Assign contacts to categories'**
  String get assignContactsToCategories;

  /// No description provided for @swipeToAssign.
  ///
  /// In en, this message translates to:
  /// **'Swipe to assign category'**
  String get swipeToAssign;

  /// No description provided for @skipContact.
  ///
  /// In en, this message translates to:
  /// **'Skip Contact'**
  String get skipContact;

  /// No description provided for @assignToCategory.
  ///
  /// In en, this message translates to:
  /// **'Assign to Category'**
  String get assignToCategory;

  /// No description provided for @organizeYourContactsIntoCategories.
  ///
  /// In en, this message translates to:
  /// **'Organize your contacts into categories'**
  String get organizeYourContactsIntoCategories;

  /// No description provided for @contactsToCategorizePlural.
  ///
  /// In en, this message translates to:
  /// **'{count} contacts to categorize'**
  String contactsToCategorizePlural(Object count);

  /// No description provided for @contactAssignedToCategory.
  ///
  /// In en, this message translates to:
  /// **'{contactName} assigned to \"{categoryName}\"'**
  String contactAssignedToCategory(String contactName, String categoryName);

  /// No description provided for @contactAllNumbersAssignedToCategory.
  ///
  /// In en, this message translates to:
  /// **'{contactName} (all {phoneCount} numbers) assigned to {categoryName}'**
  String contactAllNumbersAssignedToCategory(
      Object categoryName, Object contactName, Object phoneCount);

  /// No description provided for @contactPartialNumbersAssignedToCategory.
  ///
  /// In en, this message translates to:
  /// **'{contactName} ({successCount} of {phoneCount} numbers) assigned to {categoryName}'**
  String contactPartialNumbersAssignedToCategory(Object categoryName,
      Object contactName, Object phoneCount, Object successCount);

  /// No description provided for @refreshContacts.
  ///
  /// In en, this message translates to:
  /// **'Refresh contacts'**
  String get refreshContacts;

  /// No description provided for @allDone.
  ///
  /// In en, this message translates to:
  /// **'All Done! 🎉'**
  String get allDone;

  /// No description provided for @allYourContactsHaveBeenCategorized.
  ///
  /// In en, this message translates to:
  /// **'All your contacts have been categorized!'**
  String get allYourContactsHaveBeenCategorized;

  /// No description provided for @greatJobOrganizingYourContacts.
  ///
  /// In en, this message translates to:
  /// **'Great job organizing your contacts.'**
  String get greatJobOrganizingYourContacts;

  /// No description provided for @allContactsCategorized.
  ///
  /// In en, this message translates to:
  /// **'All contacts categorized!'**
  String get allContactsCategorized;

  /// No description provided for @categoryDescriptions.
  ///
  /// In en, this message translates to:
  /// **'Category Descriptions'**
  String get categoryDescriptions;

  /// No description provided for @assigningContact.
  ///
  /// In en, this message translates to:
  /// **'Assigning Contact'**
  String get assigningContact;

  /// No description provided for @assigningAllPhoneNumbers.
  ///
  /// In en, this message translates to:
  /// **'Assigning all {count} phone numbers for {contactName} to {categoryName}...'**
  String assigningAllPhoneNumbers(
      int count, String contactName, String categoryName);

  /// No description provided for @partialAssignment.
  ///
  /// In en, this message translates to:
  /// **'Partial Assignment'**
  String get partialAssignment;

  /// No description provided for @successfullyAssignedPhoneNumbers.
  ///
  /// In en, this message translates to:
  /// **'Successfully assigned {successCount} phone numbers for {contactName}, but {failedCount} failed. The contact has been moved from the uncategorized list.'**
  String successfullyAssignedPhoneNumbers(
      int successCount, String contactName, int failedCount);

  /// No description provided for @retryFailed.
  ///
  /// In en, this message translates to:
  /// **'Retry Failed'**
  String get retryFailed;

  /// No description provided for @connectionError.
  ///
  /// In en, this message translates to:
  /// **'Connection Error'**
  String get connectionError;

  /// No description provided for @pleaseCheckYourInternetConnection.
  ///
  /// In en, this message translates to:
  /// **'Please check your internet connection and try again.'**
  String get pleaseCheckYourInternetConnection;

  /// No description provided for @requestTimeout.
  ///
  /// In en, this message translates to:
  /// **'Request Timeout'**
  String get requestTimeout;

  /// No description provided for @requestTookTooLong.
  ///
  /// In en, this message translates to:
  /// **'The request took too long. Please try again.'**
  String get requestTookTooLong;

  /// No description provided for @assignmentFailed.
  ///
  /// In en, this message translates to:
  /// **'Assignment Failed'**
  String get assignmentFailed;

  /// No description provided for @failedToAssignContactToCategory.
  ///
  /// In en, this message translates to:
  /// **'Failed to assign {contactName} to {categoryName}. Please try again.'**
  String failedToAssignContactToCategory(
      String contactName, String categoryName);

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @noInternetConnection.
  ///
  /// In en, this message translates to:
  /// **'No Internet Connection'**
  String get noInternetConnection;

  /// No description provided for @noInternetMessage.
  ///
  /// In en, this message translates to:
  /// **'Please check your internet connection and try again'**
  String get noInternetMessage;

  /// No description provided for @categoryEditRequiresInternet.
  ///
  /// In en, this message translates to:
  /// **'Category editing requires an internet connection to sync with other users. Please connect to the internet and try again.'**
  String get categoryEditRequiresInternet;

  /// No description provided for @emergencyModeRequiresInternet.
  ///
  /// In en, this message translates to:
  /// **'Emergency mode requires an internet connection to notify other users. Please connect to the internet and try again.'**
  String get emergencyModeRequiresInternet;

  /// No description provided for @checkConnection.
  ///
  /// In en, this message translates to:
  /// **'Check Connection'**
  String get checkConnection;

  /// No description provided for @aboutApp.
  ///
  /// In en, this message translates to:
  /// **'About App'**
  String get aboutApp;

  /// No description provided for @privacyPolicy.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// No description provided for @termsOfService.
  ///
  /// In en, this message translates to:
  /// **'Terms of Service'**
  String get termsOfService;

  /// No description provided for @notifications.
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// No description provided for @enableNotifications.
  ///
  /// In en, this message translates to:
  /// **'Enable Notifications'**
  String get enableNotifications;

  /// No description provided for @disableNotifications.
  ///
  /// In en, this message translates to:
  /// **'Disable Notifications'**
  String get disableNotifications;

  /// No description provided for @notificationPermissionRequired.
  ///
  /// In en, this message translates to:
  /// **'Notification permission is required'**
  String get notificationPermissionRequired;

  /// No description provided for @notificationPermissionRequiredForTimeSlots.
  ///
  /// In en, this message translates to:
  /// **'Notification permission is required for time slot alerts'**
  String get notificationPermissionRequiredForTimeSlots;

  /// No description provided for @notificationsEnabled.
  ///
  /// In en, this message translates to:
  /// **'Notifications enabled'**
  String get notificationsEnabled;

  /// No description provided for @notificationsDisabled.
  ///
  /// In en, this message translates to:
  /// **'Notifications disabled'**
  String get notificationsDisabled;

  /// No description provided for @goodTimeToContact.
  ///
  /// In en, this message translates to:
  /// **'Good time to contact {contactName}'**
  String goodTimeToContact(String contactName);

  /// No description provided for @timeSlotStartingNow.
  ///
  /// In en, this message translates to:
  /// **'Their preferred time slot \"{dayName}: {timeRange}\" is starting now'**
  String timeSlotStartingNow(String dayName, String timeRange);

  /// No description provided for @timeSlotStartingNowDetailed.
  ///
  /// In en, this message translates to:
  /// **'Their preferred time slot \"{dayName}: {timeRange}\" is starting now. Tap to view contact details.'**
  String timeSlotStartingNowDetailed(String dayName, String timeRange);

  /// No description provided for @emergencyModeActiveNotification.
  ///
  /// In en, this message translates to:
  /// **'Emergency Mode Active - {contactName}'**
  String emergencyModeActiveNotification(String contactName);

  /// No description provided for @emergencyModeNotificationBody.
  ///
  /// In en, this message translates to:
  /// **'User is in emergency mode and cannot be contacted'**
  String get emergencyModeNotificationBody;

  /// No description provided for @calling.
  ///
  /// In en, this message translates to:
  /// **'Calling'**
  String get calling;

  /// No description provided for @callContact.
  ///
  /// In en, this message translates to:
  /// **'Call Contact'**
  String get callContact;

  /// No description provided for @sendMessage.
  ///
  /// In en, this message translates to:
  /// **'Send Message'**
  String get sendMessage;

  /// No description provided for @contactInfo.
  ///
  /// In en, this message translates to:
  /// **'Contact Info'**
  String get contactInfo;

  /// No description provided for @noContactInfo.
  ///
  /// In en, this message translates to:
  /// **'No contact information available'**
  String get noContactInfo;

  /// No description provided for @editCategories.
  ///
  /// In en, this message translates to:
  /// **'Edit Categories'**
  String get editCategories;

  /// No description provided for @categorySettings.
  ///
  /// In en, this message translates to:
  /// **'Category Settings'**
  String get categorySettings;

  /// No description provided for @categoryDescription.
  ///
  /// In en, this message translates to:
  /// **'Category Description'**
  String get categoryDescription;

  /// No description provided for @addCategoryDescription.
  ///
  /// In en, this message translates to:
  /// **'Add a description for this category'**
  String get addCategoryDescription;

  /// No description provided for @noCategoriesFound.
  ///
  /// In en, this message translates to:
  /// **'No categories found'**
  String get noCategoriesFound;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @preferences.
  ///
  /// In en, this message translates to:
  /// **'Preferences'**
  String get preferences;

  /// No description provided for @account.
  ///
  /// In en, this message translates to:
  /// **'Account'**
  String get account;

  /// No description provided for @security.
  ///
  /// In en, this message translates to:
  /// **'Security'**
  String get security;

  /// No description provided for @help.
  ///
  /// In en, this message translates to:
  /// **'Help'**
  String get help;

  /// No description provided for @support.
  ///
  /// In en, this message translates to:
  /// **'Support'**
  String get support;

  /// No description provided for @feedback.
  ///
  /// In en, this message translates to:
  /// **'Feedback'**
  String get feedback;

  /// No description provided for @confirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// No description provided for @yes.
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No description provided for @no.
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// No description provided for @apply.
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get apply;

  /// No description provided for @reset.
  ///
  /// In en, this message translates to:
  /// **'Reset'**
  String get reset;

  /// Button text to clear search history
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get clear;

  /// No description provided for @search.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// No description provided for @filter.
  ///
  /// In en, this message translates to:
  /// **'Filter'**
  String get filter;

  /// No description provided for @sort.
  ///
  /// In en, this message translates to:
  /// **'Sort'**
  String get sort;

  /// No description provided for @today.
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get today;

  /// No description provided for @yesterday.
  ///
  /// In en, this message translates to:
  /// **'Yesterday'**
  String get yesterday;

  /// No description provided for @tomorrow.
  ///
  /// In en, this message translates to:
  /// **'Tomorrow'**
  String get tomorrow;

  /// No description provided for @thisWeek.
  ///
  /// In en, this message translates to:
  /// **'This Week'**
  String get thisWeek;

  /// No description provided for @lastWeek.
  ///
  /// In en, this message translates to:
  /// **'Last Week'**
  String get lastWeek;

  /// No description provided for @nextWeek.
  ///
  /// In en, this message translates to:
  /// **'Next Week'**
  String get nextWeek;

  /// No description provided for @morning.
  ///
  /// In en, this message translates to:
  /// **'Morning'**
  String get morning;

  /// No description provided for @afternoon.
  ///
  /// In en, this message translates to:
  /// **'Afternoon'**
  String get afternoon;

  /// No description provided for @evening.
  ///
  /// In en, this message translates to:
  /// **'Evening'**
  String get evening;

  /// No description provided for @night.
  ///
  /// In en, this message translates to:
  /// **'Night'**
  String get night;

  /// No description provided for @am.
  ///
  /// In en, this message translates to:
  /// **'AM'**
  String get am;

  /// No description provided for @pm.
  ///
  /// In en, this message translates to:
  /// **'PM'**
  String get pm;

  /// No description provided for @selectTime.
  ///
  /// In en, this message translates to:
  /// **'Select Time'**
  String get selectTime;

  /// No description provided for @selectDate.
  ///
  /// In en, this message translates to:
  /// **'Select Date'**
  String get selectDate;

  /// No description provided for @selectCategory.
  ///
  /// In en, this message translates to:
  /// **'Select Category'**
  String get selectCategory;

  /// No description provided for @selectContact.
  ///
  /// In en, this message translates to:
  /// **'Select Contact'**
  String get selectContact;

  /// No description provided for @update.
  ///
  /// In en, this message translates to:
  /// **'Update'**
  String get update;

  /// No description provided for @updated.
  ///
  /// In en, this message translates to:
  /// **'Updated'**
  String get updated;

  /// No description provided for @create.
  ///
  /// In en, this message translates to:
  /// **'Create'**
  String get create;

  /// No description provided for @created.
  ///
  /// In en, this message translates to:
  /// **'Created'**
  String get created;

  /// No description provided for @remove.
  ///
  /// In en, this message translates to:
  /// **'Remove'**
  String get remove;

  /// No description provided for @removed.
  ///
  /// In en, this message translates to:
  /// **'Removed'**
  String get removed;

  /// No description provided for @add.
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get add;

  /// No description provided for @added.
  ///
  /// In en, this message translates to:
  /// **'Added'**
  String get added;

  /// No description provided for @peopleUsingContactTimes.
  ///
  /// In en, this message translates to:
  /// **'People using Contact Times'**
  String get peopleUsingContactTimes;

  /// No description provided for @permissionRequired.
  ///
  /// In en, this message translates to:
  /// **'Permission Required'**
  String get permissionRequired;

  /// No description provided for @somethingWentWrong.
  ///
  /// In en, this message translates to:
  /// **'Something went wrong'**
  String get somethingWentWrong;

  /// No description provided for @openSettings.
  ///
  /// In en, this message translates to:
  /// **'Open Settings'**
  String get openSettings;

  /// No description provided for @noContactsUsingApp.
  ///
  /// In en, this message translates to:
  /// **'No contacts using the app'**
  String get noContactsUsingApp;

  /// No description provided for @noContactsUsingAppDescription.
  ///
  /// In en, this message translates to:
  /// **'None of your contacts are using Contact Times yet.\n\nTo see contacts here:\n• Invite friends to download the app\n• Make sure they sign up with their phone numbers\n• Their numbers should be in your device contacts'**
  String get noContactsUsingAppDescription;

  /// No description provided for @contactsCount.
  ///
  /// In en, this message translates to:
  /// **'{count} contact{plural}'**
  String contactsCount(int count, String plural);

  /// No description provided for @assignCategory.
  ///
  /// In en, this message translates to:
  /// **'Assign Category'**
  String get assignCategory;

  /// No description provided for @loadingEllipsis.
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loadingEllipsis;

  /// No description provided for @errorLoadingCategories.
  ///
  /// In en, this message translates to:
  /// **'Error loading categories: {error}'**
  String errorLoadingCategories(String error);

  /// No description provided for @assignmentMayHaveFailed.
  ///
  /// In en, this message translates to:
  /// **'Assignment may have failed - please try again'**
  String get assignmentMayHaveFailed;

  /// No description provided for @errorAssigningCategory.
  ///
  /// In en, this message translates to:
  /// **'Error assigning category: {error}'**
  String errorAssigningCategory(String error);

  /// No description provided for @viewDetails.
  ///
  /// In en, this message translates to:
  /// **'View details'**
  String get viewDetails;

  /// No description provided for @failedToMakeCall.
  ///
  /// In en, this message translates to:
  /// **'Failed to make call: {error}'**
  String failedToMakeCall(String error);

  /// No description provided for @notificationEnabledOffline.
  ///
  /// In en, this message translates to:
  /// **'Notification enabled for this time slot (offline)'**
  String get notificationEnabledOffline;

  /// No description provided for @notificationDisabledOffline.
  ///
  /// In en, this message translates to:
  /// **'Notification disabled for this time slot (offline)'**
  String get notificationDisabledOffline;

  /// No description provided for @notificationEnabled.
  ///
  /// In en, this message translates to:
  /// **'Notification enabled for this time slot'**
  String get notificationEnabled;

  /// No description provided for @notificationDisabled.
  ///
  /// In en, this message translates to:
  /// **'Notification disabled for this time slot'**
  String get notificationDisabled;

  /// No description provided for @errorUpdatingNotification.
  ///
  /// In en, this message translates to:
  /// **'Error updating notification'**
  String get errorUpdatingNotification;

  /// No description provided for @notificationPreferenceExists.
  ///
  /// In en, this message translates to:
  /// **'Notification preference already exists. Please try again.'**
  String get notificationPreferenceExists;

  /// No description provided for @networkErrorCheckConnection.
  ///
  /// In en, this message translates to:
  /// **'Network error. Please check your connection and try again.'**
  String get networkErrorCheckConnection;

  /// No description provided for @callContactQuestion.
  ///
  /// In en, this message translates to:
  /// **'Call {contactName}?'**
  String callContactQuestion(String contactName);

  /// No description provided for @goodTimeToCall.
  ///
  /// In en, this message translates to:
  /// **'Good time to call'**
  String get goodTimeToCall;

  /// No description provided for @notIdealTiming.
  ///
  /// In en, this message translates to:
  /// **'Not ideal timing'**
  String get notIdealTiming;

  /// No description provided for @theirPreferredTimes.
  ///
  /// In en, this message translates to:
  /// **'Their preferred times:'**
  String get theirPreferredTimes;

  /// No description provided for @noTimeSlotsAvailable.
  ///
  /// In en, this message translates to:
  /// **'No time slots available'**
  String get noTimeSlotsAvailable;

  /// No description provided for @callNow.
  ///
  /// In en, this message translates to:
  /// **'Call Now'**
  String get callNow;

  /// No description provided for @callAnyway.
  ///
  /// In en, this message translates to:
  /// **'Call Anyway'**
  String get callAnyway;

  /// No description provided for @activeNowLabel.
  ///
  /// In en, this message translates to:
  /// **'ACTIVE NOW'**
  String get activeNowLabel;

  /// No description provided for @on.
  ///
  /// In en, this message translates to:
  /// **'ON'**
  String get on;

  /// No description provided for @off.
  ///
  /// In en, this message translates to:
  /// **'OFF'**
  String get off;

  /// No description provided for @chooseHowToCategorize.
  ///
  /// In en, this message translates to:
  /// **'Choose how to categorize {contactName}'**
  String chooseHowToCategorize(String contactName);

  /// No description provided for @availableCategories.
  ///
  /// In en, this message translates to:
  /// **'Available Categories:'**
  String get availableCategories;

  /// No description provided for @loadingContactDetails.
  ///
  /// In en, this message translates to:
  /// **'Loading contact details...'**
  String get loadingContactDetails;

  /// No description provided for @contact.
  ///
  /// In en, this message translates to:
  /// **'Contact'**
  String get contact;

  /// No description provided for @contactPreferences.
  ///
  /// In en, this message translates to:
  /// **'{contactName}\'s Preferences'**
  String contactPreferences(String contactName);

  /// No description provided for @communicationPreferences.
  ///
  /// In en, this message translates to:
  /// **'Communication preferences'**
  String get communicationPreferences;

  /// No description provided for @callingSuggestion.
  ///
  /// In en, this message translates to:
  /// **'Calling Suggestion'**
  String get callingSuggestion;

  /// No description provided for @noPreferencesSet.
  ///
  /// In en, this message translates to:
  /// **'No preferences set'**
  String get noPreferencesSet;

  /// No description provided for @contactHasntSetPreferences.
  ///
  /// In en, this message translates to:
  /// **'This contact hasn\'t set communication preferences for you yet.'**
  String get contactHasntSetPreferences;

  /// No description provided for @currentUserProfileNotFound.
  ///
  /// In en, this message translates to:
  /// **'Current user profile not found'**
  String get currentUserProfileNotFound;

  /// No description provided for @errorUpdatingNotificationWithDetails.
  ///
  /// In en, this message translates to:
  /// **'Error updating notification: {error}'**
  String errorUpdatingNotificationWithDetails(String error);

  /// No description provided for @categoryContactAnytime.
  ///
  /// In en, this message translates to:
  /// **'Anytime'**
  String get categoryContactAnytime;

  /// No description provided for @categoryPreferAnytime.
  ///
  /// In en, this message translates to:
  /// **'If It Can Wait'**
  String get categoryPreferAnytime;

  /// No description provided for @categoryContactAtTimes.
  ///
  /// In en, this message translates to:
  /// **'Preferred Times'**
  String get categoryContactAtTimes;

  /// No description provided for @categoryContactThroughMessages.
  ///
  /// In en, this message translates to:
  /// **'Messages Only'**
  String get categoryContactThroughMessages;

  /// No description provided for @categoryNoteContactAnytime.
  ///
  /// In en, this message translates to:
  /// **'Feel free to contact me at any time.'**
  String get categoryNoteContactAnytime;

  /// No description provided for @categoryNotePreferAnytime.
  ///
  /// In en, this message translates to:
  /// **'You can reach out anytime if it\'s not urgent, but I prefer to be contacted during the times below when possible.'**
  String get categoryNotePreferAnytime;

  /// No description provided for @categoryNoteContactAtTimes.
  ///
  /// In en, this message translates to:
  /// **'Please contact me during the preferred time slots listed below.'**
  String get categoryNoteContactAtTimes;

  /// No description provided for @categoryNoteContactThroughMessages.
  ///
  /// In en, this message translates to:
  /// **'I prefer to be contacted by message only, and during the times shown below.'**
  String get categoryNoteContactThroughMessages;

  /// No description provided for @callingSuggestionNoCategory.
  ///
  /// In en, this message translates to:
  /// **'This contact hasn\'t set up Contact Times yet.'**
  String get callingSuggestionNoCategory;

  /// No description provided for @callingSuggestionContactAnytime.
  ///
  /// In en, this message translates to:
  /// **'You can contact them at any time.'**
  String get callingSuggestionContactAnytime;

  /// No description provided for @callingSuggestionPreferAnytimeGoodTime.
  ///
  /// In en, this message translates to:
  /// **'Now is a preferred time to call.'**
  String get callingSuggestionPreferAnytimeGoodTime;

  /// No description provided for @callingSuggestionPreferAnytimeBadTime.
  ///
  /// In en, this message translates to:
  /// **'You can call now, but they prefer calls during their specified times.'**
  String get callingSuggestionPreferAnytimeBadTime;

  /// No description provided for @callingSuggestionContactAtTimesGoodTime.
  ///
  /// In en, this message translates to:
  /// **'Now is a good time to call.'**
  String get callingSuggestionContactAtTimesGoodTime;

  /// No description provided for @callingSuggestionContactAtTimesBadTime.
  ///
  /// In en, this message translates to:
  /// **'Please call during their specified times.'**
  String get callingSuggestionContactAtTimesBadTime;

  /// No description provided for @callingSuggestionContactThroughMessages.
  ///
  /// In en, this message translates to:
  /// **'They prefer to be contacted through messages.'**
  String get callingSuggestionContactThroughMessages;

  /// No description provided for @contactSuggestionNoCategory.
  ///
  /// In en, this message translates to:
  /// **'This contact hasn\'t set communication preferences yet. You can contact them anytime.'**
  String get contactSuggestionNoCategory;

  /// No description provided for @contactSuggestionContactAnytimeGood.
  ///
  /// In en, this message translates to:
  /// **'✅ Great time to contact! {note}'**
  String contactSuggestionContactAnytimeGood(String note);

  /// No description provided for @contactSuggestionPreferAnytimeGoodTime.
  ///
  /// In en, this message translates to:
  /// **'✅ Perfect time to contact! You\'re calling during their preferred hours.'**
  String get contactSuggestionPreferAnytimeGoodTime;

  /// No description provided for @contactSuggestionPreferAnytimeBadTime.
  ///
  /// In en, this message translates to:
  /// **'⚠️ You can contact them now, but they prefer calls during their specified times.'**
  String get contactSuggestionPreferAnytimeBadTime;

  /// No description provided for @contactSuggestionContactAtTimesGoodTime.
  ///
  /// In en, this message translates to:
  /// **'✅ Perfect time to contact! You\'re calling during their available hours.'**
  String get contactSuggestionContactAtTimesGoodTime;

  /// No description provided for @contactSuggestionContactAtTimesBadTime.
  ///
  /// In en, this message translates to:
  /// **'❌ Not the best time. They prefer to be contacted during their specified times only.'**
  String get contactSuggestionContactAtTimesBadTime;

  /// No description provided for @contactSuggestionContactThroughMessages.
  ///
  /// In en, this message translates to:
  /// **'💬 They prefer messages over calls. Consider sending a text instead.'**
  String get contactSuggestionContactThroughMessages;

  /// Compact alphabet navigation button text for both English and Arabic
  ///
  /// In en, this message translates to:
  /// **'أ-Z'**
  String get azNavigation;

  /// Header text for search history dropdown
  ///
  /// In en, this message translates to:
  /// **'Recent searches'**
  String get recentSearches;

  /// Title when no contacts match search query
  ///
  /// In en, this message translates to:
  /// **'No search results'**
  String get noSearchResults;

  /// Description when no contacts match search query
  ///
  /// In en, this message translates to:
  /// **'No contacts found matching \"{query}\".\n\nTry:\n• Checking the spelling\n• Using a different search term\n• Searching by name or phone number'**
  String noSearchResultsDescription(String query);

  /// Button text to clear current search
  ///
  /// In en, this message translates to:
  /// **'Clear search'**
  String get clearSearch;

  /// Text showing filtered search results count
  ///
  /// In en, this message translates to:
  /// **'{count} of {total} contacts'**
  String searchResults(int count, int total);

  /// Header text for alphabet navigation dropdown
  ///
  /// In en, this message translates to:
  /// **'Jump to Letter'**
  String get jumpToLetter;

  /// No description provided for @share.
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get share;

  /// No description provided for @shareApp.
  ///
  /// In en, this message translates to:
  /// **'Share App'**
  String get shareApp;

  /// No description provided for @chooseHowToShare.
  ///
  /// In en, this message translates to:
  /// **'Choose how you\'d like to share the app with {contactName}:'**
  String chooseHowToShare(String contactName);

  /// No description provided for @whatsapp.
  ///
  /// In en, this message translates to:
  /// **'WhatsApp'**
  String get whatsapp;

  /// No description provided for @sms.
  ///
  /// In en, this message translates to:
  /// **'SMS'**
  String get sms;

  /// No description provided for @otherApps.
  ///
  /// In en, this message translates to:
  /// **'Other Apps'**
  String get otherApps;

  /// No description provided for @copyLink.
  ///
  /// In en, this message translates to:
  /// **'Copy Link'**
  String get copyLink;

  /// No description provided for @whatsappOpenedWithMessage.
  ///
  /// In en, this message translates to:
  /// **'WhatsApp opened. Message copied to clipboard - paste it to share!'**
  String get whatsappOpenedWithMessage;

  /// Template message for sharing the app with contacts
  ///
  /// In en, this message translates to:
  /// **'📱 I\'m using Contact Time to manage when people can call or message me — no more missed or mistimed calls!\n\nWant to know the best time to reach me? Download Contact Time and search for my profile to see when I\'m available.\n\n👉 {appLink}\n\nSet your own contact preferences too — it\'s easy and stress-free.'**
  String shareMessage(String appLink);

  /// No description provided for @notUsingTheApp.
  ///
  /// In en, this message translates to:
  /// **'Not using the app'**
  String get notUsingTheApp;

  /// No description provided for @contactNotUsingAppYet.
  ///
  /// In en, this message translates to:
  /// **'{contactName} is not using this app yet.'**
  String contactNotUsingAppYet(String contactName);

  /// No description provided for @whatWouldYouLikeToDo.
  ///
  /// In en, this message translates to:
  /// **'What would you like to do?'**
  String get whatWouldYouLikeToDo;

  /// No description provided for @selectPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Select Phone Number'**
  String get selectPhoneNumber;

  /// No description provided for @choosePhoneNumberToCall.
  ///
  /// In en, this message translates to:
  /// **'Choose which phone number to call:'**
  String get choosePhoneNumberToCall;

  /// No description provided for @profileUpdatedOffline.
  ///
  /// In en, this message translates to:
  /// **'Profile updated offline. Will sync when online.'**
  String get profileUpdatedOffline;

  /// No description provided for @offlineMode.
  ///
  /// In en, this message translates to:
  /// **'Offline Mode'**
  String get offlineMode;

  /// No description provided for @profileUpdateOnlineOnly.
  ///
  /// In en, this message translates to:
  /// **'Profile updates and image uploads are only available when you\'re connected to the internet. Please check your connection and try again.'**
  String get profileUpdateOnlineOnly;

  /// No description provided for @backOnline.
  ///
  /// In en, this message translates to:
  /// **'You\'re back online! You can now update your profile.'**
  String get backOnline;

  /// No description provided for @stillOffline.
  ///
  /// In en, this message translates to:
  /// **'Still offline. Please check your internet connection.'**
  String get stillOffline;

  /// No description provided for @emergencyMode.
  ///
  /// In en, this message translates to:
  /// **'Emergency Mode'**
  String get emergencyMode;

  /// No description provided for @activateEmergencyMode.
  ///
  /// In en, this message translates to:
  /// **'Activate Emergency Mode'**
  String get activateEmergencyMode;

  /// No description provided for @emergencyModeActive.
  ///
  /// In en, this message translates to:
  /// **'Emergency Mode Active'**
  String get emergencyModeActive;

  /// No description provided for @emergencyModeDescription.
  ///
  /// In en, this message translates to:
  /// **'During emergency mode, contacts will see a special message when trying to call or message you.'**
  String get emergencyModeDescription;

  /// No description provided for @selectDuration.
  ///
  /// In en, this message translates to:
  /// **'Select Duration:'**
  String get selectDuration;

  /// No description provided for @hour1.
  ///
  /// In en, this message translates to:
  /// **'Hour 1'**
  String get hour1;

  /// No description provided for @hours3.
  ///
  /// In en, this message translates to:
  /// **'Hours 3'**
  String get hours3;

  /// No description provided for @hours6.
  ///
  /// In en, this message translates to:
  /// **'Hours 6'**
  String get hours6;

  /// No description provided for @hours12.
  ///
  /// In en, this message translates to:
  /// **'Hours 12'**
  String get hours12;

  /// No description provided for @day1.
  ///
  /// In en, this message translates to:
  /// **'Day 1'**
  String get day1;

  /// No description provided for @week1.
  ///
  /// In en, this message translates to:
  /// **'Week 1'**
  String get week1;

  /// No description provided for @understood.
  ///
  /// In en, this message translates to:
  /// **'Understood'**
  String get understood;

  /// No description provided for @notAvailable.
  ///
  /// In en, this message translates to:
  /// **'not available'**
  String get notAvailable;

  /// No description provided for @personalEmergency.
  ///
  /// In en, this message translates to:
  /// **'personal emergency'**
  String get personalEmergency;

  /// No description provided for @emergencyModeActiveFor.
  ///
  /// In en, this message translates to:
  /// **'Emergency Mode Active for {contactName}'**
  String emergencyModeActiveFor(String contactName);

  /// No description provided for @contactNotAvailable.
  ///
  /// In en, this message translates to:
  /// **'{contactName} is not available'**
  String contactNotAvailable(String contactName);

  /// No description provided for @emergencyModeMessage.
  ///
  /// In en, this message translates to:
  /// **'{userName} is currently in emergency mode. They are not available for calls or messages. {exceptionGroups}. Emergency mode: {remainingTime}'**
  String emergencyModeMessage(
      String userName, String exceptionGroups, String remainingTime);

  /// No description provided for @selectExceptionGroups.
  ///
  /// In en, this message translates to:
  /// **'Select Exception Groups'**
  String get selectExceptionGroups;

  /// No description provided for @exceptionGroupsDescription.
  ///
  /// In en, this message translates to:
  /// **'Select groups that can still contact you during emergency mode'**
  String get exceptionGroupsDescription;

  /// No description provided for @addCustomGroup.
  ///
  /// In en, this message translates to:
  /// **'Create New Group or Note'**
  String get addCustomGroup;

  /// No description provided for @addCustomGroupDescription.
  ///
  /// In en, this message translates to:
  /// **'You can create custom groups (e.g., \'Work Team\') or personal notes (e.g., \'Important Meeting\')'**
  String get addCustomGroupDescription;

  /// No description provided for @groupName.
  ///
  /// In en, this message translates to:
  /// **'Group/Note name'**
  String get groupName;

  /// No description provided for @family.
  ///
  /// In en, this message translates to:
  /// **'Family'**
  String get family;

  /// No description provided for @coworkers.
  ///
  /// In en, this message translates to:
  /// **'Coworkers'**
  String get coworkers;

  /// No description provided for @friends.
  ///
  /// In en, this message translates to:
  /// **'Friends'**
  String get friends;

  /// No description provided for @daysRemaining.
  ///
  /// In en, this message translates to:
  /// **'{days}d {hours}h remaining'**
  String daysRemaining(int days, int hours);

  /// No description provided for @hoursRemaining.
  ///
  /// In en, this message translates to:
  /// **'{hours}h {minutes}m remaining'**
  String hoursRemaining(int hours, int minutes);

  /// No description provided for @minutesRemaining.
  ///
  /// In en, this message translates to:
  /// **'{minutes}m remaining'**
  String minutesRemaining(int minutes);

  /// No description provided for @emergencyContactsMessage.
  ///
  /// In en, this message translates to:
  /// **'Contacts will see an emergency message when trying to reach you'**
  String get emergencyContactsMessage;

  /// No description provided for @deactivateEmergencyMode.
  ///
  /// In en, this message translates to:
  /// **'Deactivate Emergency Mode'**
  String get deactivateEmergencyMode;

  /// No description provided for @cannotMakeCallsInEmergency.
  ///
  /// In en, this message translates to:
  /// **'You cannot make calls while in emergency mode. Please deactivate emergency mode first'**
  String get cannotMakeCallsInEmergency;

  /// No description provided for @cannotContact.
  ///
  /// In en, this message translates to:
  /// **'Cannot contact {contactName}'**
  String cannotContact(String contactName);

  /// No description provided for @remaining.
  ///
  /// In en, this message translates to:
  /// **'remaining'**
  String get remaining;

  /// No description provided for @seeLess.
  ///
  /// In en, this message translates to:
  /// **'see less'**
  String get seeLess;

  /// No description provided for @tapToActivateEmergencyMode.
  ///
  /// In en, this message translates to:
  /// **'Tap to activate emergency mode'**
  String get tapToActivateEmergencyMode;

  /// No description provided for @selectCountry.
  ///
  /// In en, this message translates to:
  /// **'Select Country'**
  String get selectCountry;

  /// No description provided for @searchCountries.
  ///
  /// In en, this message translates to:
  /// **'Search countries...'**
  String get searchCountries;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar', 'en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'en':
      return AppLocalizationsEn();
  }

  throw FlutterError(
      'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue '
      'on GitHub with a reproducible sample app and the gen-l10n configuration '
      'that was used.');
}
