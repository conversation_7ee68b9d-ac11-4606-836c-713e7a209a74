// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'أوقات التواصل';

  @override
  String get appSubtitle => 'إدارة أوقات إمكانية التواصل معك';

  @override
  String get signIn => 'تسجيل الدخول';

  @override
  String get signUp => 'إنشاء حساب';

  @override
  String get signOut => 'تسجيل الخروج';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get password => 'كلمة المرور';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get fullName => 'الاسم الكامل';

  @override
  String get phoneNumber => 'رقم الهاتف';

  @override
  String get username => 'اسم المستخدم';

  @override
  String get pleaseEnterEmail => 'يرجى إدخال البريد الإلكتروني';

  @override
  String get pleaseEnterValidEmail => 'يرجى إدخال بريد إلكتروني صحيح';

  @override
  String get pleaseEnterPassword => 'يرجى إدخال كلمة المرور';

  @override
  String get pleaseEnterFullName => 'يرجى إدخال الاسم الكامل';

  @override
  String get pleaseEnterPhoneNumber => 'يرجى إدخال رقم الهاتف';

  @override
  String get pleaseEnterUsername => 'يرجى إدخال اسم المستخدم';

  @override
  String get passwordsDoNotMatch => 'كلمات المرور غير متطابقة';

  @override
  String get pleaseConfirmPassword => 'يرجى تأكيد كلمة المرور';

  @override
  String get passwordMustBeAtLeast6Characters =>
      'كلمة المرور يجب أن تكون 6 أحرف على الأقل';

  @override
  String get pleaseEnterValidPhoneNumber => 'يرجى إدخال رقم هاتف صحيح';

  @override
  String get createAccount => 'إنشاء حساب';

  @override
  String get joinContactTimesToday => 'انضم إلى أوقات التواصل اليوم';

  @override
  String get phoneNumberAlreadyRegistered => 'رقم الهاتف مسجل بالفعل';

  @override
  String get thisPhoneNumberIsAlreadyRegistered =>
      'رقم الهاتف هذا مسجل بالفعل مع حساب آخر. هل تريد تسجيل الدخول بدلاً من ذلك؟';

  @override
  String get emailAlreadyRegistered => 'البريد الإلكتروني مسجل بالفعل';

  @override
  String get thisEmailIsAlreadyRegistered =>
      'البريد الإلكتروني هذا مسجل بالفعل مع حساب آخر. هل تريد تسجيل الدخول بدلاً من ذلك؟';

  @override
  String get invalidEmail => 'بريد إلكتروني غير صحيح';

  @override
  String get weakPassword => 'كلمة مرور ضعيفة';

  @override
  String get pleaseChooseStrongerPassword =>
      'يرجى اختيار كلمة مرور أقوى تحتوي على 6 أحرف على الأقل';

  @override
  String get accountSetupFailed => 'فشل إعداد الحساب';

  @override
  String get accountCreatedButProfileSetupFailed =>
      'تم إنشاء حسابك لكن لم نتمكن من إعداد ملفك الشخصي. يرجى التواصل مع الدعم الفني';

  @override
  String get signUpFailedTitle => 'فشل إنشاء الحساب';

  @override
  String get unexpectedErrorOccurred =>
      'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى';

  @override
  String get signInFailed => 'فشل تسجيل الدخول';

  @override
  String get signUpFailed => 'فشل إنشاء الحساب';

  @override
  String get profileUpdateFailed => 'فشل تحديث الملف الشخصي';

  @override
  String get profileUpdatedSuccessfully => 'تم تحديث الملف الشخصي بنجاح';

  @override
  String get dontHaveAccount => 'ليس لديك حساب؟ إنشاء حساب';

  @override
  String get alreadyHaveAccount => 'لديك حساب بالفعل؟ تسجيل الدخول';

  @override
  String get contacts => 'جهات الاتصال';

  @override
  String get categories => 'الفئات';

  @override
  String get distribute => 'توزيع';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get dashboard => 'لوحة التحكم';

  @override
  String get loadingContacts => 'جاري تحميل جهات الاتصال...';

  @override
  String get noContactsFound => 'لم يتم العثور على جهات اتصال';

  @override
  String get contactsPermissionRequired =>
      'مطلوب إذن الوصول لجهات الاتصال لمزامنة جهات الاتصال الخاصة بك';

  @override
  String get grantPermission => 'منح الإذن';

  @override
  String get syncContacts => 'مزامنة جهات الاتصال';

  @override
  String get availableTimes => 'الأوقات المتاحة';

  @override
  String get timeSlots => 'الفترات الزمنية';

  @override
  String get addTimeSlot => 'إضافة فترة زمنية';

  @override
  String get editTimeSlots => 'تعديل الفترات الزمنية';

  @override
  String get saveTimeSlots => 'حفظ الفترات الزمنية';

  @override
  String get startTime => 'وقت البداية';

  @override
  String get endTime => 'وقت النهاية';

  @override
  String get selectDay => 'اختر اليوم';

  @override
  String get monday => 'الاثنين';

  @override
  String get tuesday => 'الثلاثاء';

  @override
  String get wednesday => 'الأربعاء';

  @override
  String get thursday => 'الخميس';

  @override
  String get friday => 'الجمعة';

  @override
  String get saturday => 'السبت';

  @override
  String get sunday => 'الأحد';

  @override
  String get activeNow => 'نشط الآن';

  @override
  String get notify => 'إشعار';

  @override
  String get call => 'اتصال';

  @override
  String get message => 'رسالة';

  @override
  String get profileInformation => 'معلومات الملف الشخصي';

  @override
  String get appInformation => 'معلومات التطبيق';

  @override
  String get version => 'الإصدار';

  @override
  String get buildNumber => 'رقم البناء';

  @override
  String get notificationSettings => 'إعدادات الإشعارات';

  @override
  String get changePhoto => 'تغيير الصورة';

  @override
  String get updateProfile => 'تحديث الملف الشخصي';

  @override
  String get aboutContactTimes => 'حول أوقات التواصل';

  @override
  String get manageWhenYouCanBeContacted =>
      'إدارة الأوقات التي يمكن للأشخاص المختلفين التواصل معك فيها. قم بإعداد الفئات والفترات الزمنية لإعلام الآخرين بتفضيلات توفرك.';

  @override
  String get loadingProfile => 'جاري تحميل الملف الشخصي...';

  @override
  String get profileNotFound => 'لم يتم العثور على الملف الشخصي';

  @override
  String get profilePictureUploadComingSoon => 'رفع صورة الملف الشخصي قريباً';

  @override
  String get noPhoneNumber => 'لا يوجد رقم هاتف';

  @override
  String get welcome => 'أهلاً وسهلاً!';

  @override
  String get failedToSignOut => 'فشل تسجيل الخروج';

  @override
  String get uploadProfilePicture => 'رفع صورة الملف الشخصي';

  @override
  String get selectImageSource => 'اختر مصدر الصورة';

  @override
  String get camera => 'الكاميرا';

  @override
  String get gallery => 'المعرض';

  @override
  String get imageUploadedSuccessfully => 'تم تحديث صورة الملف الشخصي بنجاح';

  @override
  String get failedToUploadImage => 'فشل في رفع الصورة';

  @override
  String get deleteAccount => 'حذف الحساب';

  @override
  String get deleteAccountConfirmation =>
      'هل أنت متأكد من أنك تريد حذف حسابك؟ لا يمكن التراجع عن هذا الإجراء.';

  @override
  String get deleteAccountWarning =>
      'سيؤدي هذا إلى حذف حسابك وجميع البيانات المرتبطة به نهائياً.';

  @override
  String get confirmDelete => 'تأكيد الحذف';

  @override
  String get accountDeletedSuccessfully => 'تم حذف الحساب بنجاح';

  @override
  String get failedToDeleteAccount => 'فشل في حذف الحساب';

  @override
  String get enterYourFullName => 'أدخل اسمك الكامل';

  @override
  String get enterYourUsername => 'أدخل اسم المستخدم';

  @override
  String get enterYourPhoneNumber => 'أدخل رقم هاتفك';

  @override
  String get language => 'اللغة';

  @override
  String get selectLanguage => 'اختر اللغة';

  @override
  String get english => 'English';

  @override
  String get arabic => 'العربية';

  @override
  String get save => 'حفظ';

  @override
  String get cancel => 'إلغاء';

  @override
  String get delete => 'حذف';

  @override
  String get edit => 'تعديل';

  @override
  String get done => 'تم';

  @override
  String get back => 'رجوع';

  @override
  String get next => 'التالي';

  @override
  String get previous => 'السابق';

  @override
  String get close => 'إغلاق';

  @override
  String get error => 'خطأ';

  @override
  String get success => 'نجح';

  @override
  String get warning => 'تحذير';

  @override
  String get info => 'معلومات';

  @override
  String get offline => 'غير متصل';

  @override
  String get online => 'متصل';

  @override
  String get syncing => 'جاري المزامنة...';

  @override
  String get syncComplete => 'اكتملت المزامنة';

  @override
  String get noTimeSlots => 'لم يتم تكوين فترات زمنية';

  @override
  String get addFirstTimeSlot => 'أضف فترتك الزمنية الأولى';

  @override
  String get timeSlotOverlap => 'الفترات الزمنية متداخلة وسيتم دمجها';

  @override
  String get categoryAssigned => 'تم تعيين الفئة';

  @override
  String get categoryUpdated => 'تم تحديث الفئة';

  @override
  String get contactCategorized => 'تم تصنيف جهة الاتصال بنجاح';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get refresh => 'تحديث';

  @override
  String get myCategories => 'فئاتي';

  @override
  String get pending => 'معلق';

  @override
  String get loadingYourCategories => 'جاري تحميل فئاتك...';

  @override
  String get oopsSomethingWentWrong => 'عذراً! حدث خطأ ما';

  @override
  String get tryAgain => 'حاول مرة أخرى';

  @override
  String get categoryNoteUpdatedSuccessfully => 'تم تحديث ملاحظة الفئة بنجاح';

  @override
  String get updatedLocally => 'تم التحديث محلياً. سيتم المزامنة عند الاتصال.';

  @override
  String get updatedOffline =>
      'تم التحديث في وضع عدم الاتصال. سيتم المزامنة عند الاتصال.';

  @override
  String get failedToUpdateCategory => 'فشل تحديث الفئة';

  @override
  String get timeSlotsUpdatedSuccessfully => 'تم تحديث الفترات الزمنية بنجاح';

  @override
  String get allChangesSyncedSuccessfully => 'تم مزامنة جميع التغييرات بنجاح';

  @override
  String get noDescriptionAddedYet => 'لم تتم إضافة وصف بعد...';

  @override
  String todaysTimeSlots(int count) {
    return 'فترات اليوم الزمنية ($count)';
  }

  @override
  String timeSlotsCount(int count) {
    return '$count فترة زمنية';
  }

  @override
  String get perfectTimeToContact => 'الوقت المثالي للتواصل!';

  @override
  String andMoreSlotsToday(int count) {
    return '... و $count فترات أخرى اليوم';
  }

  @override
  String noTimeSlotsForToday(int count, String plural) {
    return 'لا توجد فترات زمنية لليوم. لديك $count فترة في أيام أخرى.';
  }

  @override
  String get noTimeSlotsConfiguredYet => 'لم يتم تكوين فترات زمنية بعد';

  @override
  String editCategory(String categoryName) {
    return 'تعديل $categoryName';
  }

  @override
  String get description => 'الوصف';

  @override
  String get addDescriptionForCategory => 'أضف وصفاً لهذه الفئة...';

  @override
  String timeSlotNumber(int number) {
    return 'الفترة الزمنية $number';
  }

  @override
  String get dayOfWeek => 'يوم الأسبوع';

  @override
  String get noTimeSlotsYet => 'لا توجد فترات زمنية بعد';

  @override
  String get addYourFirstTimeSlot =>
      'أضف فترتك الزمنية الأولى للبدء.\nدع الآخرين يعرفون متى تكون متاحاً!';

  @override
  String get addYourFirstTimeSlotButton => 'أضف فترتك الزمنية الأولى';

  @override
  String get timeSlotOverlapWarning =>
      'هذه الفترة تتداخل مع أخرى وسيتم دمجها عند الحفظ';

  @override
  String get saving => 'جاري الحفظ...';

  @override
  String get saveChanges => 'حفظ التغييرات';

  @override
  String get pleaseAddAtLeastOneTimeSlot =>
      'يرجى إضافة فترة زمنية واحدة على الأقل';

  @override
  String mergedOverlappingTimeSlots(int count, String plural) {
    return 'تم دمج $count فترة زمنية متداخلة';
  }

  @override
  String failedToSaveTimeSlots(String error) {
    return 'فشل في حفظ الفترات الزمنية: $error';
  }

  @override
  String timeSlotsCountSimple(int count, String plural) {
    return '$count فترة زمنية';
  }

  @override
  String get hint => 'تلميح';

  @override
  String get enterYourFullNameHint => 'أدخل اسمك الكامل';

  @override
  String get enterYourUsernameHint => 'أدخل اسم المستخدم';

  @override
  String get enterYourPhoneNumberHint => 'أدخل رقم هاتفك';

  @override
  String get contactsPermissionDenied => 'تم رفض إذن جهات الاتصال';

  @override
  String get deviceContactsAccessRequired =>
      'مطلوب الوصول إلى جهات الاتصال في الجهاز';

  @override
  String get deviceContactsAccessDescription =>
      'يحتاج التطبيق للوصول إلى جهات الاتصال في جهازك لإظهار الأشخاص الذين يستخدمون أوقات التواصل ومزامنة معلومات الاتصال';

  @override
  String get allowContactsAccess => 'السماح بالوصول لجهات الاتصال';

  @override
  String get contactsSynced => 'تم مزامنة جهات الاتصال بنجاح';

  @override
  String get failedToSyncContacts => 'فشل في مزامنة جهات الاتصال';

  @override
  String get searchContacts => 'البحث في جهات الاتصال...';

  @override
  String get noContactsAvailable => 'لا توجد جهات اتصال متاحة';

  @override
  String get tapToAssignCategory => 'اضغط لتعيين فئة';

  @override
  String categoryAssignedTo(String contactName) {
    return 'تم تعيين الفئة لـ $contactName';
  }

  @override
  String get timeSlot => 'الفترة الزمنية';

  @override
  String get noTimeSlotsConfigured => 'لم يتم تكوين فترات زمنية';

  @override
  String get overlappingTimeSlots => 'سيتم دمج الفترات الزمنية المتداخلة';

  @override
  String get invalidTimeSlot => 'فترة زمنية غير صالحة';

  @override
  String get startTimeMustBeBeforeEndTime =>
      'يجب أن يكون وقت البداية قبل وقت النهاية';

  @override
  String invalidTimeSlotRange(String startTime, String endTime) {
    return 'نطاق زمني غير صحيح: وقت البداية ($startTime) يجب أن يكون قبل وقت النهاية ($endTime)';
  }

  @override
  String get distributeContacts => 'توزيع جهات الاتصال';

  @override
  String get assignContactsToCategories => 'تعيين جهات الاتصال إلى فئات';

  @override
  String get swipeToAssign => 'اسحب لتعيين الفئة';

  @override
  String get skipContact => 'تخطي جهة الاتصال';

  @override
  String get assignToCategory => 'تعيين إلى فئة';

  @override
  String get organizeYourContactsIntoCategories =>
      'نظم جهات الاتصال الخاصة بك في فئات';

  @override
  String contactsToCategorizePlural(Object count) {
    return '$count جهة اتصال للتصنيف';
  }

  @override
  String contactAssignedToCategory(String contactName, String categoryName) {
    return 'تم تعيين $contactName إلى $categoryName';
  }

  @override
  String contactAllNumbersAssignedToCategory(
      Object categoryName, Object contactName, Object phoneCount) {
    return 'تم تعيين $contactName (جميع الأرقام $phoneCount) إلى $categoryName';
  }

  @override
  String contactPartialNumbersAssignedToCategory(Object categoryName,
      Object contactName, Object phoneCount, Object successCount) {
    return 'تم تعيين $contactName ($successCount من $phoneCount أرقام) إلى $categoryName';
  }

  @override
  String get refreshContacts => 'تحديث جهات الاتصال';

  @override
  String get allDone => 'انتهينا! 🎉';

  @override
  String get allYourContactsHaveBeenCategorized =>
      'تم تصنيف جميع جهات الاتصال الخاصة بك!';

  @override
  String get greatJobOrganizingYourContacts =>
      'عمل رائع في تنظيم جهات الاتصال الخاصة بك.';

  @override
  String get allContactsCategorized => 'تم تصنيف جميع جهات الاتصال!';

  @override
  String get categoryDescriptions => 'أوصاف الفئات';

  @override
  String get assigningContact => 'جاري تعيين جهة الاتصال';

  @override
  String assigningAllPhoneNumbers(
      int count, String contactName, String categoryName) {
    return 'جاري تعيين جميع أرقام الهاتف الـ $count لـ $contactName إلى $categoryName...';
  }

  @override
  String get partialAssignment => 'تعيين جزئي';

  @override
  String successfullyAssignedPhoneNumbers(
      int successCount, String contactName, int failedCount) {
    return 'تم تعيين $successCount أرقام هاتف لـ $contactName بنجاح، لكن $failedCount فشل. تم نقل جهة الاتصال من قائمة غير المصنفة.';
  }

  @override
  String get retryFailed => 'إعادة المحاولة الفاشلة';

  @override
  String get connectionError => 'خطأ في الاتصال';

  @override
  String get pleaseCheckYourInternetConnection =>
      'يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.';

  @override
  String get requestTimeout => 'انتهت مهلة الطلب';

  @override
  String get requestTookTooLong =>
      'استغرق الطلب وقتاً طويلاً. يرجى المحاولة مرة أخرى.';

  @override
  String get assignmentFailed => 'فشل التعيين';

  @override
  String failedToAssignContactToCategory(
      String contactName, String categoryName) {
    return 'فشل في تعيين $contactName إلى $categoryName. يرجى المحاولة مرة أخرى.';
  }

  @override
  String get ok => 'موافق';

  @override
  String get noInternetConnection => 'لا يوجد اتصال بالإنترنت';

  @override
  String get noInternetMessage =>
      'يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى';

  @override
  String get categoryEditRequiresInternet =>
      'تحرير الفئات يتطلب اتصال بالإنترنت للمزامنة مع المستخدمين الآخرين. يرجى الاتصال بالإنترنت والمحاولة مرة أخرى.';

  @override
  String get emergencyModeRequiresInternet =>
      'وضع الطوارئ يتطلب اتصال بالإنترنت لإشعار المستخدمين الآخرين. يرجى الاتصال بالإنترنت والمحاولة مرة أخرى.';

  @override
  String get checkConnection => 'فحص الاتصال';

  @override
  String get aboutApp => 'حول التطبيق';

  @override
  String get privacyPolicy => 'سياسة الخصوصية';

  @override
  String get termsOfService => 'شروط الخدمة';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get enableNotifications => 'تفعيل الإشعارات';

  @override
  String get disableNotifications => 'إلغاء الإشعارات';

  @override
  String get notificationPermissionRequired => 'مطلوب إذن الإشعارات';

  @override
  String get notificationPermissionRequiredForTimeSlots =>
      'مطلوب إذن الإشعارات لتنبيهات الأوقات المناسبة';

  @override
  String get notificationsEnabled => 'تم تفعيل الإشعارات';

  @override
  String get notificationsDisabled => 'تم إلغاء الإشعارات';

  @override
  String goodTimeToContact(String contactName) {
    return 'وقت مناسب للتواصل مع $contactName';
  }

  @override
  String timeSlotStartingNow(String dayName, String timeRange) {
    return 'فترتهم المفضلة \"$dayName: $timeRange\" تبدأ الآن';
  }

  @override
  String timeSlotStartingNowDetailed(String dayName, String timeRange) {
    return 'فترتهم المفضلة \"$dayName: $timeRange\" تبدأ الآن. اضغط لعرض تفاصيل جهة الاتصال.';
  }

  @override
  String emergencyModeActiveNotification(String contactName) {
    return 'وضع الطوارئ مفعل - $contactName';
  }

  @override
  String get emergencyModeNotificationBody =>
      'المستخدم في وضع الطوارئ ولا يمكن التواصل معه';

  @override
  String get calling => 'الاتصال';

  @override
  String get callContact => 'اتصال بجهة الاتصال';

  @override
  String get sendMessage => 'إرسال رسالة';

  @override
  String get contactInfo => 'معلومات جهة الاتصال';

  @override
  String get noContactInfo => 'لا توجد معلومات جهة اتصال متاحة';

  @override
  String get editCategories => 'تعديل الفئات';

  @override
  String get categorySettings => 'إعدادات الفئة';

  @override
  String get categoryDescription => 'وصف الفئة';

  @override
  String get addCategoryDescription => 'أضف وصفاً لهذه الفئة';

  @override
  String get noCategoriesFound => 'لم يتم العثور على فئات';

  @override
  String get settings => 'الإعدادات';

  @override
  String get preferences => 'التفضيلات';

  @override
  String get account => 'الحساب';

  @override
  String get security => 'الأمان';

  @override
  String get help => 'المساعدة';

  @override
  String get support => 'الدعم';

  @override
  String get feedback => 'التعليقات';

  @override
  String get confirm => 'تأكيد';

  @override
  String get yes => 'نعم';

  @override
  String get no => 'لا';

  @override
  String get apply => 'تطبيق';

  @override
  String get reset => 'إعادة تعيين';

  @override
  String get clear => 'مسح';

  @override
  String get search => 'بحث';

  @override
  String get filter => 'تصفية';

  @override
  String get sort => 'ترتيب';

  @override
  String get today => 'اليوم';

  @override
  String get yesterday => 'أمس';

  @override
  String get tomorrow => 'غداً';

  @override
  String get thisWeek => 'هذا الأسبوع';

  @override
  String get lastWeek => 'الأسبوع الماضي';

  @override
  String get nextWeek => 'الأسبوع القادم';

  @override
  String get morning => 'الصباح';

  @override
  String get afternoon => 'بعد الظهر';

  @override
  String get evening => 'المساء';

  @override
  String get night => 'الليل';

  @override
  String get am => 'ص';

  @override
  String get pm => 'م';

  @override
  String get selectTime => 'اختر الوقت';

  @override
  String get selectDate => 'اختر التاريخ';

  @override
  String get selectCategory => 'اختر الفئة';

  @override
  String get selectContact => 'اختر جهة الاتصال';

  @override
  String get update => 'تحديث';

  @override
  String get updated => 'تم التحديث';

  @override
  String get create => 'إنشاء';

  @override
  String get created => 'تم الإنشاء';

  @override
  String get remove => 'إزالة';

  @override
  String get removed => 'تم الحذف';

  @override
  String get add => 'إضافة';

  @override
  String get added => 'تم الإضافة';

  @override
  String get peopleUsingContactTimes => 'الأشخاص الذين يستخدمون أوقات التواصل';

  @override
  String get permissionRequired => 'مطلوب إذن';

  @override
  String get somethingWentWrong => 'حدث خطأ ما';

  @override
  String get openSettings => 'فتح الإعدادات';

  @override
  String get noContactsUsingApp => 'لا توجد جهات اتصال تستخدم التطبيق';

  @override
  String get noContactsUsingAppDescription =>
      'لا يستخدم أي من جهات الاتصال الخاصة بك تطبيق أوقات التواصل بعد.\n\nلرؤية جهات الاتصال هنا:\n• ادع الأصدقاء لتحميل التطبيق\n• تأكد من تسجيلهم بأرقام هواتفهم\n• يجب أن تكون أرقامهم في جهات اتصال جهازك';

  @override
  String contactsCount(int count, String plural) {
    return '$count جهة اتصال';
  }

  @override
  String get assignCategory => 'تعيين فئة';

  @override
  String get loadingEllipsis => 'جاري التحميل...';

  @override
  String errorLoadingCategories(String error) {
    return 'خطأ في تحميل الفئات: $error';
  }

  @override
  String get assignmentMayHaveFailed =>
      'قد يكون التعيين فشل - يرجى المحاولة مرة أخرى';

  @override
  String errorAssigningCategory(String error) {
    return 'خطأ في تعيين الفئة: $error';
  }

  @override
  String get viewDetails => 'عرض التفاصيل';

  @override
  String failedToMakeCall(String error) {
    return 'فشل في إجراء المكالمة: $error';
  }

  @override
  String get notificationEnabledOffline =>
      'تم تفعيل الإشعار لهذه الفترة الزمنية (غير متصل)';

  @override
  String get notificationDisabledOffline =>
      'تم إلغاء الإشعار لهذه الفترة الزمنية (غير متصل)';

  @override
  String get notificationEnabled => 'تم تفعيل الإشعار لهذه الفترة الزمنية';

  @override
  String get notificationDisabled => 'تم إلغاء الإشعار لهذه الفترة الزمنية';

  @override
  String get errorUpdatingNotification => 'خطأ في تحديث الإشعار';

  @override
  String get notificationPreferenceExists =>
      'تفضيل الإشعار موجود بالفعل. يرجى المحاولة مرة أخرى.';

  @override
  String get networkErrorCheckConnection =>
      'خطأ في الشبكة. يرجى التحقق من اتصالك والمحاولة مرة أخرى.';

  @override
  String callContactQuestion(String contactName) {
    return 'اتصال بـ $contactName؟';
  }

  @override
  String get goodTimeToCall => 'وقت جيد للاتصال';

  @override
  String get notIdealTiming => 'التوقيت ليس مثالياً';

  @override
  String get theirPreferredTimes => 'أوقاتهم المفضلة:';

  @override
  String get noTimeSlotsAvailable => 'لا توجد فترات زمنية متاحة';

  @override
  String get callNow => 'اتصل الآن';

  @override
  String get callAnyway => 'اتصل على أي حال';

  @override
  String get activeNowLabel => 'نشط الآن';

  @override
  String get on => 'مفعل';

  @override
  String get off => 'معطل';

  @override
  String chooseHowToCategorize(String contactName) {
    return 'اختر كيفية تصنيف $contactName';
  }

  @override
  String get availableCategories => 'الفئات المتاحة:';

  @override
  String get loadingContactDetails => 'جاري تحميل تفاصيل جهة الاتصال...';

  @override
  String get contact => 'جهة الاتصال';

  @override
  String contactPreferences(String contactName) {
    return 'تفضيلات $contactName';
  }

  @override
  String get communicationPreferences => 'تفضيلات التواصل';

  @override
  String get callingSuggestion => 'اقتراح الاتصال';

  @override
  String get noPreferencesSet => 'لم يتم تعيين تفضيلات';

  @override
  String get contactHasntSetPreferences =>
      'لم تقم جهة الاتصال هذه بتعيين تفضيلات التواصل لك بعد.';

  @override
  String get currentUserProfileNotFound =>
      'لم يتم العثور على ملف المستخدم الحالي';

  @override
  String errorUpdatingNotificationWithDetails(String error) {
    return 'خطأ في تحديث الإشعار: $error';
  }

  @override
  String get categoryContactAnytime => 'في أي وقت';

  @override
  String get categoryPreferAnytime => 'إذا كان يمكن الانتظار';

  @override
  String get categoryContactAtTimes => 'الأوقات المفضلة';

  @override
  String get categoryContactThroughMessages => 'رسائل فقط';

  @override
  String get categoryNoteContactAnytime => 'لا تتردد في التواصل معي في أي وقت.';

  @override
  String get categoryNotePreferAnytime =>
      'يمكنك التواصل معي في أي وقت إذا لم يكن الأمر عاجلاً، لكنني أفضل أن يتم التواصل معي خلال الأوقات المذكورة أدناه عند الإمكان.';

  @override
  String get categoryNoteContactAtTimes =>
      'يرجى التواصل معي خلال الفترات الزمنية المفضلة المدرجة أدناه.';

  @override
  String get categoryNoteContactThroughMessages =>
      'أفضل أن يتم التواصل معي عبر الرسائل فقط، وخلال الأوقات الموضحة أدناه.';

  @override
  String get callingSuggestionNoCategory =>
      'لم يقم هذا الشخص بإعداد أوقات الاتصال بعد.';

  @override
  String get callingSuggestionContactAnytime => 'يمكنك الاتصال بهم في أي وقت.';

  @override
  String get callingSuggestionPreferAnytimeGoodTime => 'الآن وقت مفضل للاتصال.';

  @override
  String get callingSuggestionPreferAnytimeBadTime =>
      'يمكنك الاتصال الآن، لكنهم يفضلون المكالمات خلال أوقاتهم المحددة.';

  @override
  String get callingSuggestionContactAtTimesGoodTime => 'الآن وقت جيد للاتصال.';

  @override
  String get callingSuggestionContactAtTimesBadTime =>
      'يرجى الاتصال خلال أوقاتهم المحددة.';

  @override
  String get callingSuggestionContactThroughMessages =>
      'يفضلون التواصل عبر الرسائل.';

  @override
  String get contactSuggestionNoCategory =>
      'لم يحدد هذا الشخص تفضيلات التواصل بعد. يمكنك الاتصال بهم في أي وقت.';

  @override
  String contactSuggestionContactAnytimeGood(String note) {
    return '✅ وقت رائع للتواصل! $note';
  }

  @override
  String get contactSuggestionPreferAnytimeGoodTime =>
      '✅ وقت مثالي للتواصل! أنت تتصل خلال ساعاتهم المفضلة.';

  @override
  String get contactSuggestionPreferAnytimeBadTime =>
      '⚠️ يمكنك التواصل معهم الآن، لكنهم يفضلون المكالمات خلال أوقاتهم المحددة.';

  @override
  String get contactSuggestionContactAtTimesGoodTime =>
      '✅ وقت مثالي للتواصل! أنت تتصل خلال ساعاتهم المتاحة.';

  @override
  String get contactSuggestionContactAtTimesBadTime =>
      '❌ ليس أفضل وقت. يفضلون التواصل خلال أوقاتهم المحددة فقط.';

  @override
  String get contactSuggestionContactThroughMessages =>
      '💬 يفضلون الرسائل على المكالمات. فكر في إرسال رسالة نصية بدلاً من ذلك.';

  @override
  String get azNavigation => 'أ-Z';

  @override
  String get recentSearches => 'عمليات البحث الأخيرة';

  @override
  String get noSearchResults => 'لا توجد نتائج بحث';

  @override
  String noSearchResultsDescription(String query) {
    return 'لم يتم العثور على جهات اتصال تطابق \"$query\".\n\nجرب:\n• التحقق من الإملاء\n• استخدام مصطلح بحث مختلف\n• البحث بالاسم أو رقم الهاتف';
  }

  @override
  String get clearSearch => 'مسح البحث';

  @override
  String searchResults(int count, int total) {
    return '$count من $total جهة اتصال';
  }

  @override
  String get jumpToLetter => 'الانتقال إلى حرف';

  @override
  String get share => 'مشاركة';

  @override
  String get shareApp => 'مشاركة التطبيق';

  @override
  String chooseHowToShare(String contactName) {
    return 'اختر كيف تريد مشاركة التطبيق مع $contactName:';
  }

  @override
  String get whatsapp => 'واتساب';

  @override
  String get sms => 'رسالة نصية';

  @override
  String get otherApps => 'تطبيقات أخرى';

  @override
  String get copyLink => 'نسخ الرابط';

  @override
  String get whatsappOpenedWithMessage =>
      'تم فتح واتساب. تم نسخ الرسالة إلى الحافظة - الصقها للمشاركة!';

  @override
  String shareMessage(String appLink) {
    return '📱 أستخدم تطبيق أوقات التواصل لإدارة الأوقات التي يمكن للناس الاتصال بي أو مراسلتي فيها — لا مزيد من المكالمات الفائتة أو في الأوقات غير المناسبة!\n\nتريد معرفة أفضل وقت للتواصل معي؟ حمّل تطبيق أوقات التواصل وابحث عن ملفي الشخصي لترى متى أكون متاحاً.\n\n👉 $appLink\n\nحدد تفضيلات التواصل الخاصة بك أيضاً — الأمر سهل وخالٍ من التوتر.';
  }

  @override
  String get notUsingTheApp => 'لا يستخدم التطبيق';

  @override
  String contactNotUsingAppYet(String contactName) {
    return '$contactName لا يستخدم هذا التطبيق بعد.';
  }

  @override
  String get whatWouldYouLikeToDo => 'ماذا تريد أن تفعل؟';

  @override
  String get selectPhoneNumber => 'اختر رقم الهاتف';

  @override
  String get choosePhoneNumberToCall => 'اختر رقم الهاتف للاتصال:';

  @override
  String get profileUpdatedOffline =>
      'تم تحديث الملف الشخصي في وضع عدم الاتصال. سيتم المزامنة عند الاتصال.';

  @override
  String get offlineMode => 'وضع عدم الاتصال';

  @override
  String get profileUpdateOnlineOnly =>
      'تحديثات الملف الشخصي ورفع الصور متاحة فقط عند الاتصال بالإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى.';

  @override
  String get backOnline => 'عدت للاتصال! يمكنك الآن تحديث ملفك الشخصي.';

  @override
  String get stillOffline =>
      'لا تزال غير متصل. يرجى التحقق من اتصالك بالإنترنت.';

  @override
  String get emergencyMode => 'وضع الطوارئ';

  @override
  String get activateEmergencyMode => 'تفعيل وضع الطوارئ';

  @override
  String get emergencyModeActive => 'وضع الطوارئ مفعل';

  @override
  String get emergencyModeDescription =>
      'خلال وضع الطوارئ، ستظهر رسالة خاصة لجهات الاتصال عند محاولة الاتصال بك أو مراسلتك.';

  @override
  String get selectDuration => 'اختر المدة:';

  @override
  String get hour1 => 'ساعة واحدة';

  @override
  String get hours3 => '3 ساعات';

  @override
  String get hours6 => '6 ساعات';

  @override
  String get hours12 => '12 ساعة';

  @override
  String get day1 => 'يوم واحد';

  @override
  String get week1 => 'أسبوع واحد';

  @override
  String get understood => 'مفهوم';

  @override
  String get notAvailable => 'غير متاح';

  @override
  String get personalEmergency => 'طارئ شخصي';

  @override
  String emergencyModeActiveFor(String contactName) {
    return 'وضع الطوارئ مفعل لـ $contactName';
  }

  @override
  String contactNotAvailable(String contactName) {
    return '$contactName غير متاح';
  }

  @override
  String emergencyModeMessage(
      String userName, String exceptionGroups, String remainingTime) {
    return '$userName في وضع الطوارئ حالياً. غير متاح للمكالمات أو الرسائل. $exceptionGroups. وضع الطوارئ: $remainingTime';
  }

  @override
  String get selectExceptionGroups => 'اختر المجموعات المستثناة';

  @override
  String get exceptionGroupsDescription =>
      'اختر المجموعات التي يمكنها الاتصال بك أثناء وضع الطوارئ';

  @override
  String get addCustomGroup => 'إنشاء مجموعة جديدة أو ملاحظة';

  @override
  String get addCustomGroupDescription =>
      'يمكنك إنشاء مجموعات مخصصة (مثل \'فريق العمل\') أو ملاحظات شخصية (مثل \'اجتماع مهم\')';

  @override
  String get groupName => 'اسم المجموعة/الملاحظة';

  @override
  String get family => 'العائلة';

  @override
  String get coworkers => 'زملاء العمل';

  @override
  String get friends => 'الأصدقاء';

  @override
  String daysRemaining(int days, int hours) {
    return '$days يوم $hours ساعة متبقية';
  }

  @override
  String hoursRemaining(int hours, int minutes) {
    return '$hours ساعة $minutes دقيقة متبقية';
  }

  @override
  String minutesRemaining(int minutes) {
    return '$minutes دقيقة متبقية';
  }

  @override
  String get emergencyContactsMessage =>
      'جهات الاتصال ستشاهد رسالة طوارئ عند محاولة الوصول إليك';

  @override
  String get deactivateEmergencyMode => 'إلغاء تفعيل وضع الطوارئ';

  @override
  String get cannotMakeCallsInEmergency =>
      'لا يمكنك إجراء مكالمات أثناء وضع الطوارئ. يرجى إلغاء تفعيل وضع الطوارئ أولاً';

  @override
  String cannotContact(String contactName) {
    return 'لا يمكن الاتصال بـ $contactName';
  }

  @override
  String get remaining => 'متبقي';

  @override
  String get seeLess => 'عرض أقل';

  @override
  String get tapToActivateEmergencyMode => 'اضغط لتفعيل وضع الطوارئ';

  @override
  String get selectCountry => 'اختر الدولة';

  @override
  String get searchCountries => 'البحث عن الدول...';
}
