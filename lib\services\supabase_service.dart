import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/models.dart';
import '../models/category_extensions.dart';
import 'contacts_service.dart';
import 'local_database_service.dart';
import 'offline_contact_service.dart';
import 'data_stream_service.dart';
import 'performance_monitor_service.dart';
import 'emergency_mode_service.dart';

/// Custom HTTP overrides to handle certificate verification issues
/// This helps resolve SSL/TLS handshake errors on some devices
class _CustomHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback = (X509Certificate cert, String host, int port) {
        // Only allow certificate bypass for our Supabase host
        // This helps with certificate verification issues on some devices/networks
        return host == 'piuefmjyoamlieapemmk.supabase.co';
      };
  }
}

class SupabaseService {
  static const String supabaseUrl = 'https://piuefmjyoamlieapemmk.supabase.co';
  static const String supabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBpdWVmbWp5b2FtbGllYXBlbW1rIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzOTU5OTYsImV4cCI6MjA2Njk3MTk5Nn0.x8DPiGXkx4ykGzPQhazEoX4m4Hiya1kvMj1_P4FSUjU';

  static SupabaseClient get client => Supabase.instance.client;

  static Future<void> initialize() async {
    try {
      // Configure HTTP overrides for certificate handling
      HttpOverrides.global = _CustomHttpOverrides();

      await Supabase.initialize(
        url: supabaseUrl,
        anonKey: supabaseAnonKey,
        debug: false, // Disable debug mode in production
      );
      print('✅ Supabase initialized successfully');
    } catch (e) {
      print('❌ Supabase initialization error: $e');

      // Fallback: Try without HTTP overrides
      try {
        HttpOverrides.global = null;
        await Supabase.initialize(
          url: supabaseUrl,
          anonKey: supabaseAnonKey,
          debug: false,
        );
        print('✅ Supabase initialized with fallback method');
      } catch (fallbackError) {
        print('❌ Supabase fallback initialization failed: $fallbackError');
        rethrow;
      }
    }
  }

  // Authentication methods
  static Future<AuthResponse> signUp({
    required String email,
    required String password,
    required String fullName,
    required String phoneNumber,
  }) async {
    try {
      // First check if phone number already exists
      final existingProfile = await client
          .from('profiles')
          .select('id, full_name, phone_number')
          .eq('phone_number', phoneNumber)
          .maybeSingle();

      if (existingProfile != null) {
        throw Exception('PHONE_ALREADY_EXISTS');
      }

      // Check if email already exists by trying to sign up
      final response = await client.auth.signUp(
        email: email,
        password: password,
      );

      if (response.user != null) {
        try {
          // Create profile after successful signup
          await client.from('profiles').insert({
            'id': response.user!.id,
            'full_name': fullName,
            'phone_number': phoneNumber,
          });
        } catch (profileError) {
          // If profile creation fails, clean up the auth user
          await client.auth.signOut();

          // Check if it's a phone number constraint error
          if (profileError.toString().contains('profiles_phone_number_key') ||
              profileError
                  .toString()
                  .contains('duplicate key value violates unique constraint')) {
            throw Exception('PHONE_ALREADY_EXISTS');
          }

          throw Exception(
              'PROFILE_CREATION_FAILED: ${profileError.toString()}');
        }
      }

      return response;
    } catch (e) {
      // Re-throw our custom exceptions
      if (e.toString().contains('PHONE_ALREADY_EXISTS') ||
          e.toString().contains('PROFILE_CREATION_FAILED')) {
        rethrow;
      }

      // Handle Supabase auth errors
      if (e.toString().contains('email_address_invalid')) {
        throw Exception('EMAIL_INVALID');
      } else if (e.toString().contains('weak_password')) {
        throw Exception('PASSWORD_WEAK');
      } else if (e.toString().contains('email_already_exists') ||
          e.toString().contains('User already registered')) {
        throw Exception('EMAIL_ALREADY_EXISTS');
      }

      // Generic error
      throw Exception('SIGNUP_FAILED: ${e.toString()}');
    }
  }

  static Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    try {
      return await client.auth.signInWithPassword(
        email: email,
        password: password,
      );
    } catch (e) {
      print('❌ Sign in error: $e');

      // Handle specific certificate verification errors
      if (e.toString().contains('CERTIFICATE_VERIFY_FAILED') ||
          e.toString().contains('HandshakeException') ||
          e.toString().contains('certificate is not yet valid')) {
        throw Exception(
            'NETWORK_CERTIFICATE_ERROR: Please check your internet connection and try again. If the problem persists, try connecting to a different network.');
      }

      // Handle other network-related errors
      if (e.toString().contains('SocketException') ||
          e.toString().contains('TimeoutException') ||
          e.toString().contains('Connection refused')) {
        throw Exception(
            'NETWORK_CONNECTION_ERROR: Please check your internet connection and try again.');
      }

      // Handle authentication-specific errors
      if (e.toString().contains('Invalid login credentials') ||
          e.toString().contains('Email not confirmed') ||
          e.toString().contains('Invalid email or password')) {
        throw Exception(
            'INVALID_CREDENTIALS: Please check your email and password and try again.');
      }

      // Re-throw the original error for other cases
      rethrow;
    }
  }

  static Future<void> signOut() async {
    print('🔐 Signing out user...');

    // Clear all local data before signing out
    await _clearAllLocalData();

    await client.auth.signOut();
    print('🔐 User signed out successfully');
  }

  /// Comprehensive cleanup of all local data during sign out
  static Future<void> _clearAllLocalData() async {
    try {
      print('🧹 Starting comprehensive local data cleanup...');

      // Import required services
      final SharedPreferences prefs = await SharedPreferences.getInstance();

      // 1. Clear Local SQLite Database
      print('🧹 Clearing local database...');
      await LocalDatabaseService.clearAllData();

      // 2. Clear SharedPreferences data
      print('🧹 Clearing SharedPreferences...');
      await _clearSharedPreferences(prefs);

      // 3. Clear in-memory caches
      print('🧹 Clearing in-memory caches...');
      await _clearInMemoryCaches();

      print('✅ Local data cleanup completed successfully');
    } catch (e) {
      print('❌ Error during local data cleanup: $e');
      // Continue with sign out even if cleanup fails
    }
  }

  /// Clear all SharedPreferences data
  static Future<void> _clearSharedPreferences(SharedPreferences prefs) async {
    try {
      // Language preferences
      await prefs.remove('preferred_language');

      // Device contacts cache
      await prefs.remove('device_contacts_cache');
      await prefs.remove('device_contacts_cache_time');

      // Search history
      await prefs.remove('contacts_search_history');

      // Contact distribution position and assigned contacts
      await prefs.remove('contact_distribution_position');
      await prefs.remove('assigned_contact_ids');

      // Clear all other app-related preferences
      final keys = prefs.getKeys();
      for (final key in keys) {
        if (key.startsWith('contact_times_') ||
            key.startsWith('app_') ||
            key.contains('cache') ||
            key.contains('position') ||
            key.contains('history') ||
            key.contains('preference')) {
          await prefs.remove(key);
        }
      }

      print('✅ SharedPreferences cleared');
    } catch (e) {
      print('❌ Error clearing SharedPreferences: $e');
    }
  }

  /// Clear in-memory caches from various services
  static Future<void> _clearInMemoryCaches() async {
    try {
      // Clear OfflineContactService cache
      final offlineService = OfflineContactService();
      await offlineService.clearCache();

      // Clear DataStreamService cache
      final dataStreamService = DataStreamService();
      await dataStreamService.clearAllCaches();

      // Clear EmergencyModeService data and cache
      final emergencyService = EmergencyModeService();
      await emergencyService.clearAllEmergencyData();

      // Clear PerformanceMonitorService data
      final performanceMonitor = PerformanceMonitorService();
      performanceMonitor.clearMetrics();

      print('✅ In-memory caches cleared');
    } catch (e) {
      print('❌ Error clearing in-memory caches: $e');
    }
  }

  static User? get currentUser => client.auth.currentUser;

  static Stream<AuthState> get authStateChanges =>
      client.auth.onAuthStateChange;

  // Check if user is authenticated (offline-safe)
  static bool get isAuthenticated {
    final user = currentUser;
    final isAuth = user != null;
    print(
        '🔐 isAuthenticated check - user: ${user?.id}, authenticated: $isAuth');
    return isAuth;
  }

  // Check if phone number is already registered
  static Future<bool> isPhoneNumberRegistered(String phoneNumber) async {
    try {
      final response = await client
          .from('profiles')
          .select('id')
          .eq('phone_number', phoneNumber)
          .maybeSingle();

      return response != null;
    } catch (e) {
      // If there's an error, assume phone number is not registered
      return false;
    }
  }

  // Check if email is already registered
  static Future<bool> isEmailRegistered(String email) async {
    try {
      // We can't directly check auth.users, so we'll try a sign up and catch the error
      // This is a limitation of Supabase's public API
      return false; // For now, we'll handle this during actual sign up
    } catch (e) {
      return false;
    }
  }

  // Profile methods
  static Future<Profile?> getCurrentProfile() async {
    final user = currentUser;
    if (user == null) return null;

    try {
      final response = await client.from('profiles').select().eq('id', user.id);

      if (response.isEmpty) {
        print('🔄 Profile not found on server for user: ${user.id}');
        return null;
      }

      return Profile.fromJson(response.first);
    } catch (e) {
      print('❌ Error fetching current profile: $e');
      return null;
    }
  }

  static Future<Profile> updateProfile(Profile profile) async {
    try {
      // First try to update the existing profile
      final response = await client
          .from('profiles')
          .update(profile.toJson())
          .eq('id', profile.id)
          .select();

      if (response.isEmpty) {
        // Profile doesn't exist, create it
        print(
            '🔄 Profile not found on server, creating new profile: ${profile.id}');
        final createResponse = await client
            .from('profiles')
            .insert(profile.toJson())
            .select()
            .single();
        return Profile.fromJson(createResponse);
      } else {
        // Profile updated successfully
        return Profile.fromJson(response.first);
      }
    } catch (e) {
      print('❌ Error updating profile: $e');
      // If update fails, try to create the profile
      try {
        print(
            '🔄 Attempting to create profile after update failed: ${profile.id}');
        final createResponse = await client
            .from('profiles')
            .insert(profile.toJson())
            .select()
            .single();
        return Profile.fromJson(createResponse);
      } catch (createError) {
        print('❌ Failed to create profile: $createError');
        rethrow;
      }
    }
  }

  // File upload methods
  static Future<String> uploadProfilePicture(
      String filePath, String userId) async {
    try {
      // Check if user is authenticated
      final user = currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Use folder structure: userId/profile.jpg
      final fileName = '$userId/profile.jpg';
      final file = File(filePath);

      print('📤 Uploading profile picture for user: $userId');
      print('👤 Current auth user: ${user.id}');
      print('📁 File path: $fileName');
      print('📄 File exists: ${await file.exists()}');
      print('📊 File size: ${await file.length()} bytes');

      // Upload to Supabase Storage
      await client.storage
          .from('profile-pictures')
          .upload(fileName, file, fileOptions: const FileOptions(upsert: true));

      print('✅ Profile picture uploaded successfully');

      // Get public URL
      final publicUrl =
          client.storage.from('profile-pictures').getPublicUrl(fileName);

      print('🔗 Public URL: $publicUrl');
      return publicUrl;
    } catch (e) {
      print('❌ Error uploading profile picture: $e');
      print('❌ Error type: ${e.runtimeType}');
      if (e.toString().contains('403') ||
          e.toString().contains('Unauthorized')) {
        print(
            '🔒 This is a permissions issue. Please run the fix_storage_policies.sql script in your Supabase SQL editor.');
      }
      rethrow;
    }
  }

  static Future<void> deleteProfilePicture(String userId) async {
    try {
      // Use folder structure: userId/profile.jpg
      final fileName = '$userId/profile.jpg';
      await client.storage.from('profile-pictures').remove([fileName]);
    } catch (e) {
      print('❌ Error deleting profile picture: $e');
      // Don't rethrow - this is not critical
    }
  }

  // Account deletion using Edge Function (complete deletion)
  static Future<void> deleteAccount() async {
    try {
      final user = currentUser;
      if (user == null) throw Exception('No user logged in');

      print('🗑️ Starting complete account deletion for user: ${user.id}');
      print('🔧 Calling Edge Function: delete_user_account');

      // Call the Edge Function for complete account deletion
      final response = await client.functions.invoke('delete_user_account');

      print('📡 Edge Function response status: ${response.status}');
      print('📡 Edge Function response data: ${response.data}');

      if (response.status != 200) {
        print('❌ Edge Function failed with status: ${response.status}');
        print('❌ Error details: ${response.data}');

        // Fallback to data-only deletion if Edge Function fails
        print('🔄 Falling back to data-only deletion...');
        await deleteAccountDataOnly();
        return;
      }

      // Check if the response indicates success
      final responseData = response.data;
      if (responseData is Map && responseData['success'] == true) {
        print('🗑️ Complete account deletion completed successfully');
        print('ℹ️ Note: Both app data and auth user record have been deleted');
      } else {
        print('⚠️ Edge Function returned unexpected response: $responseData');
        print('🔄 Falling back to data-only deletion...');
        await deleteAccountDataOnly();
      }
    } catch (e) {
      print('❌ Error calling Edge Function: $e');
      print('🔄 Falling back to data-only deletion...');

      try {
        await deleteAccountDataOnly();
      } catch (fallbackError) {
        print('❌ Fallback deletion also failed: $fallbackError');
        rethrow;
      }
    }
  }

  // Account deletion (app data only) - fallback method
  static Future<void> deleteAccountDataOnly() async {
    try {
      final user = currentUser;
      if (user == null) throw Exception('No user logged in');

      print('🗑️ Starting account data deletion for user: ${user.id}');

      // Delete profile picture if exists
      try {
        await deleteProfilePicture(user.id);
        print('✅ Deleted profile picture');
      } catch (e) {
        print('⚠️ Warning: Could not delete profile picture: $e');
        // Continue with deletion even if profile picture deletion fails
      }

      // Delete user data in order (due to foreign key constraints)

      // 1. Delete notification preferences
      try {
        await client
            .from('notification_preferences')
            .delete()
            .or('user_id.eq.${user.id},contact_user_id.eq.${user.id}');
        print('✅ Deleted notification preferences');
      } catch (e) {
        print('⚠️ Warning: Could not delete notification preferences: $e');
      }

      // 2. Delete user contacts (both as categorizer and categorized)
      try {
        // Get user's phone number first
        final userProfile = await client
            .from('profiles')
            .select('phone_number')
            .eq('id', user.id)
            .single();

        final userPhone = userProfile['phone_number'] as String?;

        if (userPhone != null) {
          // Delete contacts where user is the categorizer
          await client.from('user_contacts').delete().eq('user_id', user.id);

          // Delete contacts where user is the categorized contact
          await client
              .from('user_contacts')
              .delete()
              .eq('categorized_contact_phone', userPhone);
        }
        print('✅ Deleted user contacts');
      } catch (e) {
        print('⚠️ Warning: Could not delete user contacts: $e');
      }

      // 3. Get user's category IDs first, then delete time slots
      try {
        final userCategories =
            await client.from('categories').select('id').eq('user_id', user.id);

        final categoryIds = userCategories.map((cat) => cat['id']).toList();

        if (categoryIds.isNotEmpty) {
          await client
              .from('time_slots')
              .delete()
              .inFilter('category_id', categoryIds);
          print('✅ Deleted time slots for ${categoryIds.length} categories');
        }
      } catch (e) {
        print('⚠️ Warning: Could not delete time slots: $e');
      }

      // 4. Delete categories
      try {
        await client.from('categories').delete().eq('user_id', user.id);
        print('✅ Deleted categories');
      } catch (e) {
        print('⚠️ Warning: Could not delete categories: $e');
      }

      // 5. Delete profile (this will also trigger cascade deletion due to foreign key)
      try {
        await client.from('profiles').delete().eq('id', user.id);
        print('✅ Deleted profile');
      } catch (e) {
        print('⚠️ Warning: Could not delete profile: $e');
      }

      // 6. Clear all local data before signing out
      await _clearAllLocalData();

      // 7. Sign out the user to end their session
      // Note: The auth user record will remain but all app data is deleted
      await client.auth.signOut();

      print('🗑️ Account data deletion completed successfully');
      print(
          'ℹ️ Note: Auth user record remains but all app data has been deleted');
    } catch (e) {
      print('❌ Error deleting account: $e');
      rethrow;
    }
  }

  // Categories methods
  static Future<List<Category>> getUserCategories(String userId) async {
    print('🔄 Fetching categories for user: $userId');

    // First try the normal query with time slots
    try {
      final response = await client
          .from('categories')
          .select('*, time_slots(*)')
          .eq('user_id', userId)
          .order('type');

      final categories =
          response.map<Category>((json) => Category.fromJson(json)).toList();

      print('🔄 Fetched ${categories.length} categories via normal query');
      for (final category in categories) {
        print(
            '🔄   Category: ${category.type.name} with ${category.timeSlots.length} time slots');
      }

      // If any category has no time slots, try to fetch them separately
      List<Category> categoriesWithTimeSlots = [];
      for (final category in categories) {
        if (category.timeSlots.isEmpty) {
          print(
              '🔄 Category ${category.id} has no time slots, fetching separately...');
          try {
            final timeSlotsResponse = await client
                .from('time_slots')
                .select('*')
                .eq('category_id', category.id);

            print(
                '🔄 Found ${timeSlotsResponse.length} time slots for category ${category.id}');

            if (timeSlotsResponse.isNotEmpty) {
              final timeSlots = timeSlotsResponse
                  .map<TimeSlot>((json) => TimeSlot.fromJson(json))
                  .toList();
              final categoryWithTimeSlots =
                  category.copyWith(timeSlots: timeSlots);
              categoriesWithTimeSlots.add(categoryWithTimeSlots);
              print(
                  '🔄 ✅ Added ${timeSlots.length} time slots to category ${category.type.name}');
            } else {
              categoriesWithTimeSlots.add(category);
            }
          } catch (e) {
            print(
                '🔄 ❌ Failed to fetch time slots separately for category ${category.id}: $e');
            categoriesWithTimeSlots.add(category);
          }
        } else {
          categoriesWithTimeSlots.add(category);
        }
      }

      return categoriesWithTimeSlots;
    } catch (e) {
      print('🔄 ❌ Failed to fetch categories: $e');
      rethrow;
    }
  }

  static Future<Category> updateCategory(Category category) async {
    final response = await client
        .from('categories')
        .update(category.toJson())
        .eq('id', category.id)
        .select()
        .single();

    return Category.fromJson(response);
  }

  // Time slots methods
  static Future<void> updateTimeSlots(
      String categoryId, List<TimeSlot> timeSlots) async {
    try {
      print(
          '🔄 ==================== TIME SLOTS UPDATE START ====================');
      print('🔄 Category ID: $categoryId');
      print('🔄 Time slots to update: ${timeSlots.length}');
      print('🔄 Current user: ${currentUser?.id ?? 'NOT_AUTHENTICATED'}');

      // Validate inputs
      if (categoryId.isEmpty) {
        throw Exception('Category ID cannot be empty');
      }

      // Check if category exists and belongs to current user
      print('🔄 Verifying category ownership...');
      final categoryCheck = await client
          .from('categories')
          .select('id, user_id, type')
          .eq('id', categoryId)
          .maybeSingle();

      if (categoryCheck == null) {
        throw Exception('Category not found: $categoryId');
      }

      if (categoryCheck['user_id'] != currentUser?.id) {
        throw Exception(
            'Category does not belong to current user. Category user: ${categoryCheck['user_id']}, Current user: ${currentUser?.id}');
      }

      print(
          '🔄 ✅ Category verified: ${categoryCheck['type']} (Owner: ${categoryCheck['user_id']})');

      // Log current time slots before deletion
      print('🔄 Checking existing time slots...');
      final existingSlots = await client
          .from('time_slots')
          .select('id, day_of_week, start_time, end_time')
          .eq('category_id', categoryId);

      print('🔄 Found ${existingSlots.length} existing time slots:');
      for (final slot in existingSlots) {
        print(
            '🔄   - ID: ${slot['id']}, Day: ${slot['day_of_week']}, Time: ${slot['start_time']}-${slot['end_time']}');
      }

      // Delete existing time slots for this category
      print('🔄 Deleting existing time slots...');
      await client.from('time_slots').delete().eq('category_id', categoryId);

      print('🔄 ✅ Deletion completed');

      // Insert new time slots (let Supabase generate UUIDs for new ones)
      if (timeSlots.isNotEmpty) {
        print('🔄 Preparing ${timeSlots.length} time slots for insertion...');

        final slotsData = <Map<String, dynamic>>[];

        for (int i = 0; i < timeSlots.length; i++) {
          final slot = timeSlots[i];

          // Validate time slot data
          if (slot.dayOfWeek < 0 || slot.dayOfWeek > 6) {
            throw Exception(
                'Invalid day_of_week: ${slot.dayOfWeek} (must be 0-6)');
          }

          final data = <String, dynamic>{
            'category_id': categoryId,
            'day_of_week': slot.dayOfWeek,
            'start_time': slot.startTime.toString(),
            'end_time': slot.endTime.toString(),
            'created_at': slot.createdAt.toIso8601String(),
          };

          // Only include ID if it's not empty and is a valid UUID format
          if (slot.id.isNotEmpty) {
            // Validate UUID format
            final uuidRegex = RegExp(
                r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$');
            if (uuidRegex.hasMatch(slot.id)) {
              data['id'] = slot.id;
              print('🔄   [$i] Using existing ID: ${slot.id}');
            } else {
              print(
                  '🔄   [$i] Invalid UUID format, letting Supabase generate: ${slot.id}');
            }
          } else {
            print('🔄   [$i] No ID provided, letting Supabase generate');
          }

          print(
              '🔄   [$i] Time slot data: Day=${slot.dayOfWeek} (${slot.dayName}), Time=${slot.timeRange}');
          slotsData.add(data);
        }

        print('🔄 Inserting ${slotsData.length} time slots...');
        print('🔄 Insert data: ${slotsData.map((d) => {
              'id': d['id'] ?? 'AUTO_GENERATE',
              'day': d['day_of_week'],
              'time': '${d['start_time']}-${d['end_time']}'
            }).toList()}');

        final response = await client
            .from('time_slots')
            .insert(slotsData)
            .select('id, day_of_week, start_time, end_time, created_at');

        print(
            '🔄 ✅ Time slots inserted successfully: ${response.length} records');

        // Log the results
        for (int i = 0; i < response.length; i++) {
          final record = response[i];
          print(
              '🔄   [$i] Inserted: ID=${record['id']}, Day=${record['day_of_week']}, Time=${record['start_time']}-${record['end_time']}');
        }

        // Verify insertion by querying back
        print('🔄 Verifying insertion...');
        final verifySlots = await client
            .from('time_slots')
            .select('id, day_of_week, start_time, end_time')
            .eq('category_id', categoryId);

        print(
            '🔄 ✅ Verification: Found ${verifySlots.length} time slots after insertion');
        for (final slot in verifySlots) {
          print(
              '🔄   - Verified: ID=${slot['id']}, Day=${slot['day_of_week']}, Time=${slot['start_time']}-${slot['end_time']}');
        }
      } else {
        print('🔄 No time slots to insert');
      }

      print(
          '🔄 ==================== TIME SLOTS UPDATE SUCCESS ====================');
    } catch (e, stackTrace) {
      print('🔄 ❌❌❌ TIME SLOTS UPDATE FAILED ❌❌❌');
      print('🔄 Error: $e');
      print('🔄 Stack trace: $stackTrace');
      print('🔄 Category ID: $categoryId');
      print('🔄 Time slots count: ${timeSlots.length}');
      print('🔄 Current user: ${currentUser?.id ?? 'NOT_AUTHENTICATED'}');
      print(
          '🔄 ==================== TIME SLOTS UPDATE FAILED ====================');
      rethrow;
    }
  }

  // User contacts methods
  static Future<List<UserContact>> getUserContacts(String userId) async {
    final response = await client
        .from('user_contacts')
        .select('*, categories(*)')
        .eq('user_id', userId);

    return response
        .map<UserContact>((json) => UserContact.fromJson(json))
        .toList();
  }

  // Get a category by ID
  static Future<Category?> getCategoryById(String categoryId) async {
    try {
      final response = await client
          .from('categories')
          .select('*')
          .eq('id', categoryId)
          .single();

      return Category.fromJson(response);
    } catch (e) {
      print('❌ Error fetching category by ID: $e');
      return null;
    }
  }

  // Get user contacts where a specific phone number was categorized (reverse lookup)
  static Future<List<UserContact>> getUserContactsByCategorizedPhone(
      String phoneNumber) async {
    print('🔍 getUserContactsByCategorizedPhone called with: $phoneNumber');

    // Normalize the phone number for matching
    final normalizedPhone =
        ContactsService.normalizePhoneForMatching(phoneNumber);
    print('🔍 Normalized phone: $normalizedPhone');

    // Get all user_contacts and filter by normalized phone
    final response =
        await client.from('user_contacts').select('*, categories(*)');

    final allUserContacts = response
        .map<UserContact>((json) => UserContact.fromJson(json))
        .toList();
    print('🔍 Total user_contacts from server: ${allUserContacts.length}');

    // Filter by phone number using robust matching and ensure categories are loaded
    final matchingContacts = <UserContact>[];
    for (final userContact in allUserContacts) {
      final matches = ContactsService.doPhoneNumbersMatch(
          userContact.categorizedContactPhone, phoneNumber);
      if (matches) {
        print(
            '🔍 ✅ Found match: ${userContact.userId} categorized ${userContact.categorizedContactPhone} -> ${userContact.assignedCategoryId}');
        print(
            '🔍 Category in match: ${userContact.category?.type.name ?? 'NULL'}');

        // If category is null, try to fetch it separately
        if (userContact.category == null) {
          print(
              '🔍 Category is null, fetching separately for ID: ${userContact.assignedCategoryId}');
          try {
            final categoryResponse = await client
                .from('categories')
                .select('*')
                .eq('id', userContact.assignedCategoryId)
                .single();

            final category = Category.fromJson(categoryResponse);
            print('🔍 Fetched category separately: ${category.type.name}');

            // Create a new UserContact with the category
            final userContactWithCategory = UserContact(
              id: userContact.id,
              userId: userContact.userId,
              categorizedContactPhone: userContact.categorizedContactPhone,
              assignedCategoryId: userContact.assignedCategoryId,
              createdAt: userContact.createdAt,
              updatedAt: userContact.updatedAt,
              category: category,
            );
            matchingContacts.add(userContactWithCategory);
          } catch (e) {
            print('🔍 ❌ Failed to fetch category separately: $e');
            matchingContacts.add(userContact);
          }
        } else {
          matchingContacts.add(userContact);
        }
      }
    }

    print('🔍 Returning ${matchingContacts.length} matching user contacts');
    return matchingContacts;
  }

  static Future<UserContact> assignContactToCategory({
    required String userId,
    required String contactPhone,
    required String categoryId,
  }) async {
    try {
      // Use the phone number as provided (don't normalize) to match Distribute tab behavior
      print('🔄 Assigning category:');
      print('🔄   User ID: $userId');
      print('🔄   Contact phone (as provided): $contactPhone');
      print('🔄   Category ID: $categoryId');

      // Check what records exist for this user before assignment
      final allUserContactsResponse = await client
          .from('user_contacts')
          .select('categorized_contact_phone, assigned_category_id')
          .eq('user_id', userId);

      print('🔄 Existing user_contacts before assignment:');
      for (final contact in allUserContactsResponse) {
        print(
            '🔄   Phone: ${contact['categorized_contact_phone']}, Category: ${contact['assigned_category_id']}');
      }

      // First, try to find existing record with exact phone match
      final existingResponse = await client
          .from('user_contacts')
          .select('id, categorized_contact_phone, assigned_category_id')
          .eq('user_id', userId)
          .eq('categorized_contact_phone', contactPhone)
          .maybeSingle();

      print('🔄 Existing record found: ${existingResponse != null}');
      if (existingResponse != null) {
        print(
            '🔄   Existing phone: ${existingResponse['categorized_contact_phone']}');
        print(
            '🔄   Existing category: ${existingResponse['assigned_category_id']}');
      }

      if (existingResponse != null) {
        // Update existing record
        print('🔄 Updating existing user_contact record');
        print('🔄   Record ID: ${existingResponse['id']}');
        print('🔄   Old category: ${existingResponse['assigned_category_id']}');
        print('🔄   New category: $categoryId');

        final response = await client
            .from('user_contacts')
            .update({'assigned_category_id': categoryId})
            .eq('id', existingResponse['id'])
            .select('*, categories(*)')
            .single();

        print('🔄 Update completed successfully');

        // Clear emergency mode cache instantly for this contact to ensure immediate updates
        EmergencyModeService.clearAllCache();

        return UserContact.fromJson(response);
      } else {
        // Insert new record
        print('🔄 Creating new user_contact record');
        print('🔄   Phone: $contactPhone');
        print('🔄   Category: $categoryId');

        final response = await client
            .from('user_contacts')
            .insert({
              'user_id': userId,
              'categorized_contact_phone': contactPhone,
              'assigned_category_id': categoryId,
            })
            .select('*, categories(*)')
            .single();

        print('🔄 Insert completed successfully');

        // Clear emergency mode cache instantly for this contact to ensure immediate updates
        EmergencyModeService.clearAllCache();

        return UserContact.fromJson(response);
      }
    } catch (e) {
      print('❌ Error in assignContactToCategory: $e');
      rethrow;
    }
  }

  /// Enhanced category assignment logic for contacts screen
  /// When userA assigns a category to userB, this method:
  /// 1. Fetches current user_id (userA) from user_contacts table
  /// 2. Retrieves all categorized_contact_phone numbers for userA
  /// 3. Matches them with all userB phone numbers from device contacts
  /// 4. Updates ALL userB phone numbers (existing + new) with the new assigned category
  /// 5. Ensures consistent categorization across all userB phone numbers
  /// 6. If no match, assigns category to all userB phone numbers
  static Future<List<UserContact>> assignCategoryToContactWithAllPhones({
    required String userAId,
    required List<String> userBPhoneNumbers,
    required String categoryId,
  }) async {
    try {
      print(
          '🔄 ==================== ENHANCED CATEGORY ASSIGNMENT START ====================');
      print('🔄 UserA ID (who is assigning): $userAId');
      print('🔄 UserB phone numbers (being categorized): $userBPhoneNumbers');
      print('🔄 Category ID: $categoryId');

      // Step 1: Fetch all existing user_contacts for userA
      // (which may have many users' phone numbers: userB, userC, userD, userE, userF, etc.)
      final existingUserContactsResponse = await client
          .from('user_contacts')
          .select('id, categorized_contact_phone, assigned_category_id')
          .eq('user_id', userAId);

      print(
          '🔄 Step 1: Found ${existingUserContactsResponse.length} existing user_contacts for userA');
      for (final contact in existingUserContactsResponse) {
        final phone = contact['categorized_contact_phone'] as String;
        final categoryId = contact['assigned_category_id'] as String;
        print('🔄   Existing: $phone -> $categoryId');
      }

      // Step 2: Clean all userB phone numbers from device contacts
      final allUserBPhones = <String>[];
      for (final userBPhone in userBPhoneNumbers) {
        final cleanedUserBPhone = userBPhone.replaceAll(RegExp(r'[^\d]'), '');
        if (cleanedUserBPhone.isNotEmpty) {
          allUserBPhones.add(cleanedUserBPhone);
        }
      }

      print(
          '🔄 Step 2: UserB phones from device contacts (${allUserBPhones.length}): $allUserBPhones');

      // Step 3: Find existing userB-related phones using phone matching
      final userBRelatedRecords = <Map<String, dynamic>>[];

      for (final existingRecord in existingUserContactsResponse) {
        final existingPhone =
            existingRecord['categorized_contact_phone'] as String;

        // Check if this existing phone matches any of userB's phones
        for (final userBPhone in allUserBPhones) {
          if (ContactsService.doPhoneNumbersMatch(existingPhone, userBPhone)) {
            userBRelatedRecords.add(existingRecord);
            print(
                '🔄 Step 3: Found existing userB phone: $existingPhone matches $userBPhone');
            break; // Found match, move to next existing record
          }
        }
      }

      print(
          '🔄 Step 3: Found ${userBRelatedRecords.length} existing userB-related records');

      final results = <UserContact>[];

      // Step 4: Update existing userB phones + Insert new userB phones

      // 4A: Update ALL existing userB phones with the new category
      for (final record in userBRelatedRecords) {
        try {
          final updateResponse = await client
              .from('user_contacts')
              .update({'assigned_category_id': categoryId})
              .eq('id', record['id'])
              .select('*, categories(*)')
              .single();

          results.add(UserContact.fromJson(updateResponse));
          print(
              '🔄 Step 4A: Updated existing userB phone: ${record['categorized_contact_phone']}');
        } catch (e) {
          print(
              '🔄 Step 4A: Failed to update phone ${record['categorized_contact_phone']}: $e');
        }
      }

      // 4B: Insert new userB phones that don't exist yet
      for (final userBPhone in allUserBPhones) {
        bool alreadyExists = false;

        // Check if this userB phone already exists in the database
        for (final record in userBRelatedRecords) {
          final existingPhone = record['categorized_contact_phone'] as String;
          if (ContactsService.doPhoneNumbersMatch(userBPhone, existingPhone)) {
            alreadyExists = true;
            break;
          }
        }

        if (!alreadyExists) {
          try {
            final insertResponse = await client
                .from('user_contacts')
                .insert({
                  'user_id': userAId,
                  'categorized_contact_phone': userBPhone,
                  'assigned_category_id': categoryId,
                })
                .select('*, categories(*)')
                .single();

            results.add(UserContact.fromJson(insertResponse));
            print('🔄 Step 4B: Inserted new userB phone: $userBPhone');
          } catch (e) {
            print('🔄 Step 4B: Failed to insert phone $userBPhone: $e');
          }
        }
      }

      // Step 5: Handle edge cases
      if (existingUserContactsResponse.isEmpty) {
        print(
            '🔄 Step 5: UserA has no existing user_contacts - all userB phones were inserted as new');
      } else if (userBRelatedRecords.isEmpty) {
        print(
            '🔄 Step 5: No existing userB phones found - all userB phones were inserted as new');
      } else {
        print(
            '🔄 Step 5: Updated ${userBRelatedRecords.length} existing userB phones and inserted ${results.length - userBRelatedRecords.length} new phones');
      }

      print(
          '🔄 ==================== ENHANCED CATEGORY ASSIGNMENT SUCCESS ====================');
      print('🔄 Total assignments processed: ${results.length}');

      // Clear emergency mode cache instantly for all assigned contacts to ensure immediate updates
      EmergencyModeService.clearAllCache();

      return results;
    } catch (e) {
      print('🔄 ❌❌❌ ENHANCED CATEGORY ASSIGNMENT FAILED ❌❌❌');
      print('🔄 Error: $e');
      print('🔄 UserA ID: $userAId');
      print('🔄 UserB phones: $userBPhoneNumbers');
      print('🔄 Category ID: $categoryId');
      print(
          '🔄 ==================== ENHANCED CATEGORY ASSIGNMENT FAILED ====================');
      rethrow;
    }
  }

  static Future<void> removeContactAssignment(String contactId) async {
    await client.from('user_contacts').delete().eq('id', contactId);
  }

  // Find contacts who use the app
  static Future<List<Profile>> findContactsUsingApp(
      List<String> phoneNumbers) async {
    if (phoneNumbers.isEmpty) return [];

    final response = await client
        .from('profiles')
        .select()
        .inFilter('phone_number', phoneNumbers);

    return response.map<Profile>((json) => Profile.fromJson(json)).toList();
  }

  // Get category assigned BY current user TO a specific contact
  static Future<Category?> getCategoryAssignedToContact({
    required String currentUserId,
    required String contactPhone,
  }) async {
    try {
      // Don't normalize - use the phone as provided to match Distribute tab behavior
      print('🔍 Getting assigned category:');
      print('🔍   User ID: $currentUserId');
      print('🔍   Contact phone (as provided): $contactPhone');

      // First, let's see what phone numbers are actually stored for this user
      final allUserContactsResponse = await client
          .from('user_contacts')
          .select('categorized_contact_phone, assigned_category_id')
          .eq('user_id', currentUserId);

      print('🔍 All user_contacts for user $currentUserId:');
      for (final contact in allUserContactsResponse) {
        print('🔍   Stored phone: ${contact['categorized_contact_phone']}');
      }

      // Find the user_contact record where:
      // - user_id = currentUserId (who did the categorizing)
      // - categorized_contact_phone matches contactPhone (who was categorized)

      // First try exact match
      var userContactResponse = await client
          .from('user_contacts')
          .select('categorized_contact_phone, assigned_category_id')
          .eq('user_id', currentUserId)
          .eq('categorized_contact_phone', contactPhone);

      print(
          '🔍 Found ${userContactResponse.length} matching user_contacts for phone: $contactPhone');

      // If no exact match, try with phone number variations
      if (userContactResponse.isEmpty) {
        print('🔍 No exact match found, trying phone variations...');

        // Generate phone variations for better matching
        final phoneVariations =
            ContactsService.generatePhoneVariations(contactPhone);
        print(
            '🔍 Generated ${phoneVariations.length} phone variations: $phoneVariations');

        // Try each variation
        for (final variation in phoneVariations) {
          final variationResponse = await client
              .from('user_contacts')
              .select('categorized_contact_phone, assigned_category_id')
              .eq('user_id', currentUserId)
              .eq('categorized_contact_phone', variation);

          if (variationResponse.isNotEmpty) {
            print('🔍 Found match with variation: $variation');
            userContactResponse = variationResponse;
            break;
          }
        }

        // If still no match, try reverse lookup - check if any stored numbers match our variations
        if (userContactResponse.isEmpty) {
          print('🔍 No variation match found, trying reverse lookup...');

          // Get all user contacts for this user
          final allUserContacts = await client
              .from('user_contacts')
              .select('categorized_contact_phone, assigned_category_id')
              .eq('user_id', currentUserId);

          // Check if any stored phone matches our variations
          for (final contact in allUserContacts) {
            final storedPhone = contact['categorized_contact_phone'] as String;
            final storedVariations =
                ContactsService.generatePhoneVariations(storedPhone);

            // Check if any of our variations match any stored variations
            for (final ourVariation in phoneVariations) {
              if (storedVariations.contains(ourVariation)) {
                print(
                    '🔍 Found reverse match: stored=$storedPhone matches our variation=$ourVariation');
                userContactResponse = [contact];
                break;
              }
            }

            if (userContactResponse.isNotEmpty) break;
          }
        }
      }

      if (userContactResponse.isEmpty) {
        print(
            '🔍 No category assigned to this contact after all matching attempts');
        return null;
      }

      final matchingCategoryId =
          userContactResponse.first['assigned_category_id'] as String;
      print('🔍 Found category ID: $matchingCategoryId');

      // Fetch the actual category with time slots
      final categoryResponse = await client
          .from('categories')
          .select('*, time_slots(*)')
          .eq('id', matchingCategoryId)
          .eq('user_id', currentUserId)
          .maybeSingle();

      if (categoryResponse == null) {
        print('🔍 Category not found in database');
        return null;
      }

      print('🔍 Retrieved category: ${categoryResponse['type']}');
      return Category.fromJson(categoryResponse);
    } catch (e) {
      print('❌ Error in getCategoryAssignedToContact: $e');
      return null;
    }
  }

  // Get contact's category for current user
  static Future<Category?> getContactCategory({
    required String contactUserId,
    required String callerPhone,
  }) async {
    print('🔍 getContactCategory called:');
    print('🔍   contactUserId: $contactUserId (who did the categorizing)');
    print('🔍   callerPhone: $callerPhone (who was categorized)');

    try {
      // Step 1: Normalize the caller's phone number
      final normalizedCallerPhone =
          ContactsService.normalizePhoneForMatching(callerPhone);
      print('🔍   normalizedCallerPhone: $normalizedCallerPhone');

      // Step 2: Find the specific user_contact record where:
      // - user_id = contactUserId (who did the categorizing)
      // - categorized_contact_phone matches callerPhone (who was categorized)
      final userContactResponse = await client
          .from('user_contacts')
          .select('categorized_contact_phone, assigned_category_id')
          .eq('user_id', contactUserId);

      print(
          '🔍 Found ${userContactResponse.length} user_contacts for contactUserId: $contactUserId');

      // Step 3: Find matching phone number using robust matching
      String? matchingCategoryId;
      for (final userContact in userContactResponse) {
        final storedPhone = userContact['categorized_contact_phone'] as String;

        if (ContactsService.doPhoneNumbersMatch(storedPhone, callerPhone)) {
          matchingCategoryId = userContact['assigned_category_id'] as String;
          print(
              '✅ Found matching user_contact! Category ID: $matchingCategoryId');
          break;
        }
      }

      if (matchingCategoryId == null) {
        print(
            '🔍 No matching user_contact found for phone: $normalizedCallerPhone');
        return null;
      }

      // Step 4: Fetch the actual category with time slots
      // The category should belong to the contactUserId (who did the categorizing)
      final categoryResponse = await client
          .from('categories')
          .select('*, time_slots(*)')
          .eq('id', matchingCategoryId)
          .eq('user_id',
              contactUserId) // Ensure category belongs to the right user
          .maybeSingle();

      if (categoryResponse == null) {
        print(
            '🔍 No category found with ID: $matchingCategoryId for user: $contactUserId');
        return null;
      }

      print(
          '✅ Found category: ${categoryResponse['type']} for user: $contactUserId');
      return Category.fromJson(categoryResponse);
    } catch (e) {
      print('❌ Error in getContactCategory: $e');
      rethrow;
    }
  }

  // Notification preferences methods
  static Future<List<NotificationPreference>> getNotificationPreferences({
    required String userId,
    required String contactUserId,
  }) async {
    final response = await client
        .from('notification_preferences')
        .select()
        .eq('user_id', userId)
        .eq('contact_user_id', contactUserId);

    return response
        .map<NotificationPreference>(
            (json) => NotificationPreference.fromJson(json))
        .toList();
  }

  static Future<NotificationPreference> toggleNotificationPreference({
    required String userId,
    required String contactUserId,
    required String timeSlotId,
    required bool isEnabled,
  }) async {
    try {
      // First, try to find existing preference
      final existing = await client
          .from('notification_preferences')
          .select()
          .eq('user_id', userId)
          .eq('contact_user_id', contactUserId)
          .eq('time_slot_id', timeSlotId)
          .maybeSingle();

      if (existing != null) {
        // Update existing preference
        final response = await client
            .from('notification_preferences')
            .update({
              'is_enabled': isEnabled,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('id', existing['id'])
            .select()
            .single();

        return NotificationPreference.fromJson(response);
      } else {
        // Insert new preference
        final response = await client
            .from('notification_preferences')
            .insert({
              'user_id': userId,
              'contact_user_id': contactUserId,
              'time_slot_id': timeSlotId,
              'is_enabled': isEnabled,
            })
            .select()
            .single();

        return NotificationPreference.fromJson(response);
      }
    } catch (e) {
      print('Error in toggleNotificationPreference: $e');
      rethrow;
    }
  }

  static Future<void> removeNotificationPreference(String preferenceId) async {
    await client
        .from('notification_preferences')
        .delete()
        .eq('id', preferenceId);
  }

  // Get all notification preferences for a user
  static Future<List<NotificationPreference>> getAllUserNotificationPreferences(
      String userId) async {
    final response = await client
        .from('notification_preferences')
        .select()
        .eq('user_id', userId)
        .eq('is_enabled', true);

    return response
        .map<NotificationPreference>(
            (json) => NotificationPreference.fromJson(json))
        .toList();
  }

  // Get time slot by ID
  static Future<TimeSlot?> getTimeSlotById(String timeSlotId) async {
    final response = await client
        .from('time_slots')
        .select()
        .eq('id', timeSlotId)
        .maybeSingle();

    if (response == null) return null;
    return TimeSlot.fromJson(response);
  }

  // Get profile by ID
  static Future<Profile?> getProfileById(String profileId) async {
    final response = await client
        .from('profiles')
        .select()
        .eq('id', profileId)
        .maybeSingle();

    if (response == null) return null;
    return Profile.fromJson(response);
  }

  // Check if it's a good time to contact someone
  static bool isGoodTimeToContact(Category? category) {
    if (category == null) return true; // No preferences set, assume it's okay

    switch (category.type) {
      case CategoryType.contactAnytime:
        return true;
      case CategoryType.preferAnytime:
        // Check if current time is in preferred slots, but allow anyway
        return true;
      case CategoryType.contactAtTimes:
        // Only allow if current time is in the specified slots
        return category.timeSlots.any((slot) => slot.isCurrentTimeInSlot());
      case CategoryType.contactThroughMessages:
        // Suggest messaging instead
        return false;
    }
  }

  static String getContactSuggestion(Category? category, BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    if (category == null) {
      return l10n.contactSuggestionNoCategory;
    }

    switch (category.type) {
      case CategoryType.contactAnytime:
        // Use localized default note instead of hardcoded database note
        final localizedNote = category.type.getDefaultNote(context);
        return l10n.contactSuggestionContactAnytimeGood(localizedNote);
      case CategoryType.preferAnytime:
        final activeSlots = category.timeSlots
            .where((slot) => slot.isCurrentTimeInSlot())
            .toList();
        if (activeSlots.isNotEmpty) {
          return l10n.contactSuggestionPreferAnytimeGoodTime;
        } else {
          return l10n.contactSuggestionPreferAnytimeBadTime;
        }
      case CategoryType.contactAtTimes:
        final activeSlots = category.timeSlots
            .where((slot) => slot.isCurrentTimeInSlot())
            .toList();
        if (activeSlots.isNotEmpty) {
          return l10n.contactSuggestionContactAtTimesGoodTime;
        } else {
          return l10n.contactSuggestionContactAtTimesBadTime;
        }
      case CategoryType.contactThroughMessages:
        return l10n.contactSuggestionContactThroughMessages;
    }
  }
}
