// ignore_for_file: avoid_print

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'connectivity_service.dart';
import 'contacts_service.dart';
import 'local_database_service.dart';
import 'supabase_service.dart';
import '../models/models.dart' as models;

class SyncService extends ChangeNotifier {
  static final SyncService _instance = SyncService._internal();
  factory SyncService() => _instance;
  SyncService._internal();

  final ConnectivityService _connectivityService = ConnectivityService();
  bool _isSyncing = false;
  bool get isSyncing => _isSyncing;

  Timer? _syncTimer;
  static const Duration _syncInterval = Duration(minutes: 5);

  Future<void> initialize() async {
    print('🔄 Initializing SyncService...');

    // Listen for connectivity changes
    _connectivityService.addOnlineCallback(_onConnectivityRestored);

    // Start periodic sync when online
    if (_connectivityService.isOnline) {
      _startPeriodicSync();
    }

    print('🔄 SyncService initialized');
  }

  void _onConnectivityRestored() {
    print('🔄 Connectivity restored - starting sync...');
    syncPendingOperations();
    _startPeriodicSync();
  }

  void _startPeriodicSync() {
    _syncTimer?.cancel();
    _syncTimer = Timer.periodic(_syncInterval, (timer) {
      if (_connectivityService.isOnline && !_isSyncing) {
        syncPendingOperations();
      }
    });
  }

  void _stopPeriodicSync() {
    _syncTimer?.cancel();
    _syncTimer = null;
  }

  Future<void> syncPendingOperations() async {
    if (_isSyncing || !_connectivityService.isOnline) {
      print('🔄 Sync skipped - already syncing or offline');
      return;
    }

    _isSyncing = true;
    notifyListeners();

    try {
      print('🔄 Starting sync of pending operations...');

      final pendingOperations =
          await LocalDatabaseService.getPendingSyncOperations();
      print('🔄 Found ${pendingOperations.length} pending operations');

      for (final operation in pendingOperations) {
        try {
          await _processSyncOperation(operation);
          await LocalDatabaseService.removeSyncOperation(operation['id']);
          print(
              '✅ Synced operation: ${operation['operation_type']} for ${operation['table_name']}');
        } catch (e) {
          print('❌ Failed to sync operation ${operation['id']}: $e');
          await LocalDatabaseService.incrementRetryCount(operation['id']);

          // Remove operations that have failed too many times
          final retryCount = operation['retry_count'] is String
              ? int.tryParse(operation['retry_count']) ?? 0
              : operation['retry_count'] as int? ?? 0;

          if (retryCount >= 3) {
            print(
                '🗑️ Removing operation after 3 failed attempts: ${operation['id']}');
            await LocalDatabaseService.removeSyncOperation(operation['id']);
          }
        }
      }

      // Sync notifications
      await _syncNotifications();

      print('🔄 Sync completed successfully');
    } catch (e) {
      print('❌ Sync failed: $e');
    } finally {
      _isSyncing = false;
      notifyListeners();
    }
  }

  Future<void> _processSyncOperation(Map<String, dynamic> operation) async {
    final operationType = operation['operation_type'];
    final tableName = operation['table_name'];
    final data = jsonDecode(operation['data']);

    switch (operationType) {
      case 'INSERT_USER_CONTACT':
        await SupabaseService.assignContactToCategory(
          userId: data['user_id'],
          contactPhone: data['categorized_contact_phone'],
          categoryId: data['assigned_category_id'],
        );
        // Mark the local record as synced
        await LocalDatabaseService.markUserContactSynced(
          userId: data['user_id'],
          contactPhone: data['categorized_contact_phone'],
        );
        print('✅ User contact synced to server');
        break;

      case 'UPDATE_USER_CONTACT':
        await SupabaseService.assignContactToCategory(
          userId: data['user_id'],
          contactPhone: data['categorized_contact_phone'],
          categoryId: data['assigned_category_id'],
        );
        // Mark the local record as synced
        await LocalDatabaseService.markUserContactSynced(
          userId: data['user_id'],
          contactPhone: data['categorized_contact_phone'],
        );
        print('✅ User contact updated on server');
        break;

      case 'SEND_NOTIFICATION':
        // Handle notification sync if needed
        print('🔔 Syncing notification: ${data['contact_name']}');
        break;

      case 'UPDATE_NOTIFICATION_PREFERENCE':
        await _syncNotificationPreference(data);
        break;

      case 'UPDATE_CATEGORY':
        // Handle category updates (time slots, notes, etc.)
        final category = models.Category.fromJson(data);
        await SupabaseService.updateCategory(category);
        print('✅ Category updated on server: ${category.type.name}');
        break;

      case 'UPDATE':
        // Handle profile updates
        if (tableName == 'profiles') {
          final profile = models.Profile.fromJson(data);
          await SupabaseService.updateProfile(profile);
          print('✅ Profile updated on server: ${profile.fullName}');
        } else {
          print('⚠️ Unknown table for UPDATE operation: $tableName');
        }
        break;

      case 'UPLOAD_IMAGE':
        // Handle image uploads
        if (tableName == 'profiles') {
          await _syncImageUpload(data);
        } else {
          print('⚠️ Unknown table for UPLOAD_IMAGE operation: $tableName');
        }
        break;

      default:
        print('⚠️ Unknown operation type: $operationType');
    }
  }

  Future<void> _syncNotificationPreference(Map<String, dynamic> data) async {
    try {
      await SupabaseService.toggleNotificationPreference(
        userId: data['user_id'],
        contactUserId: data['contact_user_id'],
        timeSlotId: data['time_slot_id'],
        isEnabled: data['is_enabled'],
      );
      print('✅ Synced notification preference to server');
    } catch (e) {
      print('❌ Failed to sync notification preference: $e');
      rethrow;
    }
  }

  Future<void> _syncImageUpload(Map<String, dynamic> data) async {
    try {
      final localImagePath = data['local_image_path'] as String;
      final profileId = data['profile_id'] as String;

      // Check if local file still exists
      final file = File(localImagePath);
      if (!file.existsSync()) {
        print('⚠️ Local image file no longer exists: $localImagePath');
        return;
      }

      // Upload image to server
      final imageUrl = await SupabaseService.uploadProfilePicture(
        localImagePath,
        profileId,
      );

      // Update profile with new image URL
      final currentProfile = await SupabaseService.getCurrentProfile();
      if (currentProfile != null) {
        final updatedProfile = currentProfile.copyWith(
          avatarUrl: imageUrl,
          updatedAt: DateTime.now(),
        );
        await SupabaseService.updateProfile(updatedProfile);
        print('✅ Profile image uploaded and updated on server');
      }
    } catch (e) {
      print('❌ Failed to sync image upload: $e');
      rethrow;
    }
  }

  Future<void> _syncNotifications() async {
    try {
      final unsyncedNotifications =
          await LocalDatabaseService.getUnsyncedNotifications();
      print('🔔 Found ${unsyncedNotifications.length} unsynced notifications');

      for (final notification in unsyncedNotifications) {
        try {
          // Here you could send the notification data to the server if needed
          // For now, we'll just mark them as synced since notifications are local
          await LocalDatabaseService.markNotificationSynced(notification['id']);
          print(
              '✅ Marked notification as synced: ${notification['contact_name']}');
        } catch (e) {
          print('❌ Failed to sync notification ${notification['id']}: $e');
        }
      }
    } catch (e) {
      print('❌ Failed to sync notifications: $e');
    }
  }

  // Offline operations that will be synced later
  Future<void> assignContactToCategoryOffline({
    required String userId,
    required String contactPhone,
    required String categoryId,
  }) async {
    print('📱 Assigning contact to category offline...');
    print('📱   User: $userId');
    print('📱   Phone: $contactPhone');
    print('📱   Category: $categoryId');

    try {
      // Check if assignment already exists
      final existingCategoryId =
          await LocalDatabaseService.getAssignedCategoryId(
        userId: userId,
        contactPhone: contactPhone,
      );

      if (existingCategoryId != null) {
        // Update existing assignment
        await LocalDatabaseService.updateUserContactCategory(
          userId: userId,
          contactPhone: contactPhone,
          categoryId: categoryId,
        );

        // Add to sync queue
        await LocalDatabaseService.addToSyncQueue(
          operationType: 'UPDATE_USER_CONTACT',
          tableName: 'user_contacts',
          recordId: '$userId-$contactPhone',
          data: {
            'user_id': userId,
            'categorized_contact_phone': contactPhone,
            'assigned_category_id': categoryId,
          },
        );
      } else {
        // Create new assignment
        final id = DateTime.now().millisecondsSinceEpoch.toString();
        await LocalDatabaseService.insertUserContact(
          id: id,
          userId: userId,
          contactPhone: contactPhone,
          categoryId: categoryId,
        );

        // Add to sync queue
        await LocalDatabaseService.addToSyncQueue(
          operationType: 'INSERT_USER_CONTACT',
          tableName: 'user_contacts',
          recordId: id,
          data: {
            'user_id': userId,
            'categorized_contact_phone': contactPhone,
            'assigned_category_id': categoryId,
          },
        );
      }

      print('✅ Contact assignment saved offline and queued for sync');
    } catch (e) {
      print('❌ Failed to assign contact offline: $e');
      rethrow;
    }
  }

  Future<void> scheduleNotificationOffline({
    required String contactPhone,
    required String contactName,
    required String timeSlotId,
    required DateTime scheduledTime,
  }) async {
    print('📱 Scheduling notification offline...');

    try {
      await LocalDatabaseService.insertLocalNotification(
        contactPhone: contactPhone,
        contactName: contactName,
        timeSlotId: timeSlotId,
        scheduledTime: scheduledTime,
      );

      // Add to sync queue if needed
      await LocalDatabaseService.addToSyncQueue(
        operationType: 'SEND_NOTIFICATION',
        tableName: 'local_notifications',
        recordId: DateTime.now().millisecondsSinceEpoch.toString(),
        data: {
          'contact_phone': contactPhone,
          'contact_name': contactName,
          'time_slot_id': timeSlotId,
          'scheduled_time': scheduledTime.toIso8601String(),
        },
      );

      print('✅ Notification scheduled offline and queued for sync');
    } catch (e) {
      print('❌ Failed to schedule notification offline: $e');
      rethrow;
    }
  }

  // Data loading methods for offline use
  Future<models.Category?> getCategoryAssignedToContactOffline({
    required String currentUserId,
    required String contactPhone,
  }) async {
    try {
      final categoryId = await LocalDatabaseService.getAssignedCategoryId(
        userId: currentUserId,
        contactPhone: contactPhone,
      );

      if (categoryId != null) {
        return await LocalDatabaseService.getCategoryById(categoryId);
      }
      return null;
    } catch (e) {
      print('❌ Failed to get assigned category offline: $e');
      return null;
    }
  }

  Future<List<models.Category>> getUserCategoriesOffline(String userId) async {
    try {
      return await LocalDatabaseService.getCategoriesByUserId(userId);
    } catch (e) {
      print('❌ Failed to get user categories offline: $e');
      return [];
    }
  }

  // Get how a contact has categorized the current user (reverse lookup)
  Future<models.Category?> getContactCategoryOffline({
    required String contactUserId,
    required String callerPhone,
    List<String>?
        contactPhoneNumbers, // Optional: all contact's phone numbers for better matching
  }) async {
    try {
      print('🔍 getContactCategoryOffline called:');
      print('🔍   contactUserId: $contactUserId');
      print('🔍   callerPhone: $callerPhone');
      if (contactPhoneNumbers != null) {
        print('🔍   contactPhoneNumbers: $contactPhoneNumbers');
      }

      // Get all user_contacts for this contact user
      final db = await LocalDatabaseService.database;
      final userContactMaps = await db.query(
        'user_contacts',
        columns: ['categorized_contact_phone', 'assigned_category_id'],
        where: 'user_id = ?',
        whereArgs: [contactUserId],
      );

      print(
          '🔍   Found ${userContactMaps.length} user_contacts for contactUserId: $contactUserId');
      for (final userContact in userContactMaps) {
        print(
            '🔍     - Phone: ${userContact['categorized_contact_phone']}, Category: ${userContact['assigned_category_id']}');
      }

      if (userContactMaps.isEmpty) {
        print(
            '🔍   ❌ No user_contacts found for contactUserId: $contactUserId');
        return null;
      }

      // Strategy 1: Try to match the current user's phone number (callerPhone)
      // against the stored categorized_contact_phone numbers
      String? matchingCategoryId;

      // Generate all possible variations of the current user's phone number
      final callerPhoneVariations =
          ContactsService.generatePhoneVariations(callerPhone);
      print(
          '🔍   Generated ${callerPhoneVariations.length} variations for caller phone: $callerPhoneVariations');

      for (final userContact in userContactMaps) {
        final storedPhone = userContact['categorized_contact_phone'] as String;
        final storedPhoneVariations =
            ContactsService.generatePhoneVariations(storedPhone);

        // Check if any variation of caller phone matches any variation of stored phone
        bool foundMatch = false;
        for (final callerVariation in callerPhoneVariations) {
          for (final storedVariation in storedPhoneVariations) {
            if (callerVariation.isNotEmpty &&
                storedVariation.isNotEmpty &&
                callerVariation == storedVariation) {
              matchingCategoryId =
                  userContact['assigned_category_id'] as String;
              print(
                  '🔍   ✅ Found matching category via caller phone: $matchingCategoryId');
              print('🔍   ✅ Match: "$callerVariation" == "$storedVariation"');
              foundMatch = true;
              break;
            }
          }
          if (foundMatch) break;
        }
        if (foundMatch) break;
      }

      // Strategy 2: If no match found and we have contact's phone numbers,
      // try to match contact's phone numbers against stored categorized_contact_phone
      if (matchingCategoryId == null &&
          contactPhoneNumbers != null &&
          contactPhoneNumbers.isNotEmpty) {
        print(
            '🔍   No match via caller phone, trying contact phone numbers...');

        for (final contactPhone in contactPhoneNumbers) {
          final contactPhoneVariations =
              ContactsService.generatePhoneVariations(contactPhone);
          print(
              '🔍   Checking contact phone: $contactPhone (${contactPhoneVariations.length} variations)');

          for (final userContact in userContactMaps) {
            final storedPhone =
                userContact['categorized_contact_phone'] as String;
            final storedPhoneVariations =
                ContactsService.generatePhoneVariations(storedPhone);

            // Check if any variation of contact phone matches any variation of stored phone
            bool foundMatch = false;
            for (final contactVariation in contactPhoneVariations) {
              for (final storedVariation in storedPhoneVariations) {
                if (contactVariation.isNotEmpty &&
                    storedVariation.isNotEmpty &&
                    contactVariation == storedVariation) {
                  matchingCategoryId =
                      userContact['assigned_category_id'] as String;
                  print(
                      '🔍   ✅ Found matching category via contact phone: $matchingCategoryId');
                  print(
                      '🔍   ✅ Match: "$contactVariation" == "$storedVariation"');
                  foundMatch = true;
                  break;
                }
              }
              if (foundMatch) break;
            }
            if (foundMatch) break;
          }
          if (matchingCategoryId != null) break;
        }
      }

      if (matchingCategoryId != null) {
        final category =
            await LocalDatabaseService.getCategoryById(matchingCategoryId);
        print('🔍   ✅ Returning category: ${category?.type.name}');
        if (category != null) {
          print('🔍   Category has ${category.timeSlots.length} time slots');
          for (final slot in category.timeSlots) {
            print('🔍     - ${slot.dayName}: ${slot.timeRange}');
          }
        }
        return category;
      }

      print('🔍   ❌ No matching category found');
      return null;
    } catch (e) {
      print('❌ Failed to get contact category offline: $e');
      return null;
    }
  }

  @override
  void dispose() {
    _stopPeriodicSync();
    _connectivityService.removeOnlineCallback(_onConnectivityRestored);
    super.dispose();
  }
}
