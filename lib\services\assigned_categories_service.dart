// ignore_for_file: avoid_print

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/models.dart' as models;
import 'supabase_service.dart';
import 'connectivity_service.dart';
import 'contacts_service.dart';
import 'category_assignment_service.dart';

// Cache entry for assigned categories (categories that current user assigns TO contacts)
class AssignedCategoryCache {
  final models.Category? category;
  final DateTime cachedAt;

  AssignedCategoryCache({
    required this.category,
    required this.cachedAt,
  });

  bool get isExpired {
    // Cache expires after 15 minutes for offline scenarios
    return DateTime.now().difference(cachedAt).inMinutes > 15;
  }
}

class AssignedCategoriesService {
  static final AssignedCategoriesService _instance =
      AssignedCategoriesService._internal();
  factory AssignedCategoriesService() => _instance;
  AssignedCategoriesService._internal();

  // Cache for assigned categories (currentUserId_contactPhone -> Category)
  static final Map<String, AssignedCategoryCache> _assignedCategoriesCache = {};

  // Getter for cache (for external access)
  static Map<String, AssignedCategoryCache> get assignedCategoriesCache =>
      _assignedCategoriesCache;
  static Timer? _cacheCleanupTimer;

  // Real-time subscription for assigned category updates
  static RealtimeChannel? _assignedCategoriesSubscription;

  // Real-time subscription for time slots updates (to handle category content changes)
  static RealtimeChannel? _timeSlotsSubscription;

  // SharedPreferences keys for persistent assigned categories cache
  static const String _assignedCategoriesCacheKey = 'assigned_categories_cache';

  // Stream controller for real-time assigned category updates
  static final StreamController<Map<String, models.Category?>>
      _assignedCategoriesUpdateController =
      StreamController<Map<String, models.Category?>>.broadcast();

  // Stream for listening to assigned category updates
  static Stream<Map<String, models.Category?>>
      get assignedCategoriesUpdatesStream =>
          _assignedCategoriesUpdateController.stream;

  // Connection monitoring
  static Timer? _connectionMonitorTimer;
  static DateTime? _lastUpdateReceived;
  static bool _isSubscriptionActive = false;

  // Initialize the service
  static Future<void> initialize() async {
    print('📱 Initializing AssignedCategoriesService...');
    await loadPersistentAssignedCategoriesCache();
    _startCacheCleanup();
    _startConnectionMonitoring();
    await startRealtimeSubscription();

    // Check if this is a fresh install (no cache) and refresh if needed
    await _checkAndRefreshAfterFreshInstall();
  }

  // Check if this is a fresh install and refresh assigned categories if needed
  static Future<void> _checkAndRefreshAfterFreshInstall() async {
    try {
      // If cache is empty, this might be a fresh install
      if (_assignedCategoriesCache.isEmpty) {
        print(
            '📱 Empty cache detected - checking if this is a fresh install...');

        final currentUser = SupabaseService.currentUser;
        if (currentUser != null) {
          // Check if user has any assigned categories on the server
          final hasServerData =
              await _checkIfUserHasAssignedCategories(currentUser.id);

          if (hasServerData) {
            print(
                '📱 Fresh install detected with existing server data - will refresh on contact load');
            // Don't preload all data here to avoid performance issues
            // Instead, let individual contacts load their data as needed
            // The fixed _loadAssignedCategoryFromCache will handle this properly
          } else {
            print('📱 New user or no assigned categories on server');
          }
        }
      } else {
        print('📱 Cache found - not a fresh install');
      }
    } catch (e) {
      print('❌ Error checking fresh install status: $e');
    }
  }

  // Check if user has any assigned categories on the server
  static Future<bool> _checkIfUserHasAssignedCategories(String userId) async {
    try {
      final response = await SupabaseService.client
          .from('user_contacts')
          .select('id')
          .eq('user_id', userId)
          .limit(1);

      return response.isNotEmpty;
    } catch (e) {
      print('❌ Error checking server data: $e');
      return false;
    }
  }

  // Get assigned category for a contact (cache-first approach)
  static Future<models.Category?> getAssignedCategory({
    required String currentUserId,
    required String contactPhone,
  }) async {
    try {
      // Generate cache key
      final phoneVariations =
          ContactsService.generatePhoneVariations(contactPhone);
      AssignedCategoryCache? cachedEntry;

      // Check cache for any phone variation
      for (final variation in phoneVariations) {
        final key = '${currentUserId}_$variation';
        final entry = _assignedCategoriesCache[key];
        if (entry != null && !entry.isExpired) {
          cachedEntry = entry;
          break;
        }
      }

      if (cachedEntry != null) {
        // Return cached result whether it's a category or null (no category assigned)
        print(
            '📱 Using cached assigned category for contact $contactPhone: ${cachedEntry.category?.type.name ?? 'none (cached)'}');
        return cachedEntry.category;
      }

      print(
          '🔄 Fetching fresh assigned category for contact $contactPhone from server');

      final connectivityService = ConnectivityService();
      if (!connectivityService.isOnline) {
        print('📱 Offline - checking cached and persistent data...');

        // First try cached data (even if expired)
        for (final variation in phoneVariations) {
          final key = '${currentUserId}_$variation';
          final entry = _assignedCategoriesCache[key];
          if (entry?.category != null) {
            final category = entry!.category!;
            print(
                '📱 Using expired cached assigned category offline: ${category.type.name}');
            return category;
          }
        }

        // If no cached data, try to load from persistent storage
        print('📱 No cached data found, loading from persistent storage...');
        await loadPersistentAssignedCategoriesCache();

        // Check cache again after loading from persistent storage
        for (final variation in phoneVariations) {
          final key = '${currentUserId}_$variation';
          final entry = _assignedCategoriesCache[key];
          if (entry?.category != null) {
            print(
                '📱 Found assigned category in persistent storage: ${entry!.category!.type.name}');
            return entry.category;
          }
        }

        print('📱 No assigned category found in cache or persistent storage');
        return null;
      }

      // Fetch from server
      final category = await SupabaseService.getCategoryAssignedToContact(
        currentUserId: currentUserId,
        contactPhone: contactPhone,
      );

      // Cache the result for all phone variations (including null results)
      final cacheTime = DateTime.now();
      for (final variation in phoneVariations) {
        final key = '${currentUserId}_$variation';
        _assignedCategoriesCache[key] = AssignedCategoryCache(
          category: category, // This can be null - we cache null results too!
          cachedAt: cacheTime,
        );
      }

      // Save to persistent cache for offline access
      await _savePersistentAssignedCategoriesCache();

      // Emit assigned category update for real-time UI updates
      _emitAssignedCategoryUpdate(contactPhone, category);

      if (category != null) {
        print(
            '✅ Assigned category for contact $contactPhone: ${category.type.name} (cached)');
      } else {
        print(
            '✅ No assigned category for contact $contactPhone (cached as null)');
      }
      return category;
    } catch (e) {
      print('❌ Failed to get assigned category: $e');
      // Return cached data if available
      final phoneVariations =
          ContactsService.generatePhoneVariations(contactPhone);
      for (final variation in phoneVariations) {
        final key = '${currentUserId}_$variation';
        final cachedEntry = _assignedCategoriesCache[key];
        if (cachedEntry != null) {
          return cachedEntry.category;
        }
      }
      return null;
    }
  }

  // Get cached assigned category without making network calls
  static models.Category? getCachedAssignedCategory({
    required String currentUserId,
    required String contactPhone,
  }) {
    final phoneVariations =
        ContactsService.generatePhoneVariations(contactPhone);

    for (final variation in phoneVariations) {
      final key = '${currentUserId}_$variation';
      final cachedEntry = _assignedCategoriesCache[key];
      if (cachedEntry != null) {
        // Return cached data (including null results) even if expired when checking cache only
        print(
            '📱 Found cached assigned category for $contactPhone: ${cachedEntry.category?.type.name ?? 'none (from cache)'}');
        return cachedEntry.category;
      }
    }

    print('📱 No cached assigned category found for $contactPhone');
    return null;
  }

  // Check if a contact has been checked before (has cache entry, regardless of result)
  static bool hasBeenChecked({
    required String currentUserId,
    required String contactPhone,
  }) {
    final phoneVariations =
        ContactsService.generatePhoneVariations(contactPhone);

    for (final variation in phoneVariations) {
      final key = '${currentUserId}_$variation';
      final cachedEntry = _assignedCategoriesCache[key];
      if (cachedEntry != null && !cachedEntry.isExpired) {
        return true; // Has been checked before (whether result was null or category)
      }
    }

    return false; // Never been checked
  }

  // Check if we have any cached assigned category data (for fresh install detection)
  static bool hasCachedData() {
    return _assignedCategoriesCache.isNotEmpty;
  }

  // Update assigned category cache when assignment changes
  static void updateAssignedCategoryCache({
    required String currentUserId,
    required String contactPhone,
    required models.Category? category,
  }) {
    final phoneVariations =
        ContactsService.generatePhoneVariations(contactPhone);
    final cacheTime = DateTime.now();

    // Update cache for all phone variations
    for (final variation in phoneVariations) {
      final key = '${currentUserId}_$variation';
      if (category != null) {
        _assignedCategoriesCache[key] = AssignedCategoryCache(
          category: category,
          cachedAt: cacheTime,
        );
      } else {
        _assignedCategoriesCache.remove(key);
      }
    }

    // Save to persistent cache
    _savePersistentAssignedCategoriesCache();

    // Emit update for UI refresh
    _emitAssignedCategoryUpdate(contactPhone, category);

    print(
        '📱 Updated assigned category cache for $contactPhone: ${category?.type.name ?? 'none'}');
  }

  // Proactively fetch and cache assigned categories for multiple contacts
  static Future<void> proactivelyFetchAssignedCategories({
    required String currentUserId,
    required List<String> contactPhones,
  }) async {
    final connectivityService = ConnectivityService();
    if (!connectivityService.isOnline) {
      print(
          '📱 Device is offline - skipping proactive assigned categories fetch');
      return;
    }

    try {
      print(
          '📱 Proactively fetching assigned categories for ${contactPhones.length} contacts...');

      // Fetch assigned categories for each contact in parallel (but limit concurrency)
      final futures = <Future<void>>[];
      for (final contactPhone in contactPhones) {
        final future = getAssignedCategory(
          currentUserId: currentUserId,
          contactPhone: contactPhone,
        );
        futures.add(future);

        // Limit concurrent requests to avoid overwhelming the server
        if (futures.length >= 5) {
          await Future.wait(futures);
          futures.clear();
          // Small delay to be respectful to the server
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }

      // Wait for remaining requests
      if (futures.isNotEmpty) {
        await Future.wait(futures);
      }

      print(
          '📱 Completed proactive assigned categories fetch for all contacts');
    } catch (e) {
      print('❌ Failed to proactively fetch assigned categories: $e');
    }
  }

  // Emit assigned category update for real-time UI updates
  static void _emitAssignedCategoryUpdate(
      String contactPhone, models.Category? category) {
    try {
      if (!_assignedCategoriesUpdateController.isClosed) {
        _assignedCategoriesUpdateController.add({contactPhone: category});
        print(
            '📱 Emitted assigned category update for phone $contactPhone: ${category?.type.name ?? 'none'}');
      }
    } catch (e) {
      print('❌ Failed to emit assigned category update: $e');
    }
  }

  // Clear assigned category cache for a specific contact
  static void clearCacheForContact(String currentUserId, String contactPhone) {
    final phoneVariations =
        ContactsService.generatePhoneVariations(contactPhone);
    for (final variation in phoneVariations) {
      final key = '${currentUserId}_$variation';
      _assignedCategoriesCache.remove(key);
    }
    print('🗑️ Cleared assigned category cache for contact $contactPhone');
  }

  // Clear all assigned category cache
  static void clearAllCache() {
    _assignedCategoriesCache.clear();
    print('🗑️ Cleared all assigned category cache');
  }

  // Load persistent assigned categories cache from SharedPreferences
  static Future<void> loadPersistentAssignedCategoriesCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheDataString = prefs.getString(_assignedCategoriesCacheKey);

      if (cacheDataString != null) {
        final cacheData = jsonDecode(cacheDataString) as Map<String, dynamic>;

        for (final entry in cacheData.entries) {
          final cacheKey = entry.key;
          final assignmentData = entry.value as Map<String, dynamic>;

          models.Category? category;
          if (assignmentData['category'] != null) {
            final categoryData =
                assignmentData['category'] as Map<String, dynamic>;
            category = models.Category.fromJson(categoryData);
          }

          final cachedAt = DateTime.parse(assignmentData['cachedAt'] as String);

          _assignedCategoriesCache[cacheKey] = AssignedCategoryCache(
            category: category,
            cachedAt: cachedAt,
          );
        }

        print(
            '📱 Loaded ${_assignedCategoriesCache.length} assigned categories from persistent cache');

        // Debug: Show what was loaded
        for (final entry in _assignedCategoriesCache.entries) {
          final cacheEntry = entry.value;
          print(
              '📱 Cached: ${entry.key} -> ${cacheEntry.category?.type.name ?? 'null'} (${cacheEntry.isExpired ? 'expired' : 'valid'})');
        }
      }
    } catch (e) {
      print('❌ Failed to load persistent assigned categories cache: $e');
    }
  }

  // Save persistent assigned categories cache to SharedPreferences
  static Future<void> _savePersistentAssignedCategoriesCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheData = <String, dynamic>{};

      for (final entry in _assignedCategoriesCache.entries) {
        cacheData[entry.key] = {
          'category': entry.value.category?.toJson(),
          'cachedAt': entry.value.cachedAt.toIso8601String(),
        };
      }

      await prefs.setString(_assignedCategoriesCacheKey, jsonEncode(cacheData));
      print(
          '📱 Saved ${_assignedCategoriesCache.length} assigned categories to persistent cache');
    } catch (e) {
      print('❌ Failed to save persistent assigned categories cache: $e');
    }
  }

  // Start cache cleanup timer
  static void _startCacheCleanup() {
    _cacheCleanupTimer?.cancel();
    _cacheCleanupTimer = Timer.periodic(const Duration(minutes: 10), (timer) {
      final expiredKeys = <String>[];

      for (final entry in _assignedCategoriesCache.entries) {
        if (entry.value.isExpired) {
          expiredKeys.add(entry.key);
        }
      }

      for (final key in expiredKeys) {
        _assignedCategoriesCache.remove(key);
      }

      if (expiredKeys.isNotEmpty) {
        print(
            '🗑️ Cleaned up ${expiredKeys.length} expired assigned category cache entries');
      }
    });
  }

  // Start real-time subscription for assigned category updates
  static Future<void> startRealtimeSubscription() async {
    try {
      final connectivityService = ConnectivityService();
      if (!connectivityService.isOnline) {
        print(
            '📱 Device is offline - skipping assigned categories subscription');
        return;
      }

      print('📱 Starting real-time assigned categories subscription...');

      // Remove existing subscription if any
      await stopRealtimeSubscription();

      // Create new subscription to user_contacts table with connection monitoring
      _assignedCategoriesSubscription = SupabaseService.client
          .channel('assigned_categories_updates')
          .onPostgresChanges(
            event: PostgresChangeEvent.insert,
            schema: 'public',
            table: 'user_contacts',
            callback: (payload) {
              print(
                  '📱 Real-time assigned category insert: ${payload.newRecord}');
              _lastUpdateReceived = DateTime.now();
              _handleRealtimeAssignedCategoryUpdate(payload);
            },
          )
          .onPostgresChanges(
            event: PostgresChangeEvent.update,
            schema: 'public',
            table: 'user_contacts',
            callback: (payload) {
              print(
                  '📱 Real-time assigned category update: ${payload.newRecord}');
              _lastUpdateReceived = DateTime.now();
              _handleRealtimeAssignedCategoryUpdate(payload);
            },
          )
          .onPostgresChanges(
            event: PostgresChangeEvent.delete,
            schema: 'public',
            table: 'user_contacts',
            callback: (payload) {
              print(
                  '📱 Real-time assigned category delete: ${payload.oldRecord}');
              _lastUpdateReceived = DateTime.now();
              _handleRealtimeAssignedCategoryDelete(payload);
            },
          )
          .subscribe();

      // Create a separate subscription for time_slots table to handle category content changes
      _timeSlotsSubscription = SupabaseService.client
          .channel('time_slots_updates')
          .onPostgresChanges(
            event: PostgresChangeEvent.insert,
            schema: 'public',
            table: 'time_slots',
            callback: (payload) {
              print('📱 Real-time time slot insert: ${payload.newRecord}');
              _lastUpdateReceived = DateTime.now();
              _handleRealtimeTimeSlotChange(payload);
            },
          )
          .onPostgresChanges(
            event: PostgresChangeEvent.update,
            schema: 'public',
            table: 'time_slots',
            callback: (payload) {
              print('📱 Real-time time slot update: ${payload.newRecord}');
              _lastUpdateReceived = DateTime.now();
              _handleRealtimeTimeSlotChange(payload);
            },
          )
          .onPostgresChanges(
            event: PostgresChangeEvent.delete,
            schema: 'public',
            table: 'time_slots',
            callback: (payload) {
              print('📱 Real-time time slot delete: ${payload.oldRecord}');
              _lastUpdateReceived = DateTime.now();
              _handleRealtimeTimeSlotChange(payload);
            },
          )
          .subscribe();

      _isSubscriptionActive = true;
      _lastUpdateReceived = DateTime.now();
      print(
          '✅ Real-time assigned categories and time slots subscriptions started with monitoring');
    } catch (e) {
      print('❌ Failed to start real-time subscriptions: $e');
    }
  }

  // Stop real-time subscription
  static Future<void> stopRealtimeSubscription() async {
    try {
      if (_assignedCategoriesSubscription != null) {
        await SupabaseService.client
            .removeChannel(_assignedCategoriesSubscription!);
        _assignedCategoriesSubscription = null;
        print('📱 Real-time assigned categories subscription stopped');
      }

      if (_timeSlotsSubscription != null) {
        await SupabaseService.client.removeChannel(_timeSlotsSubscription!);
        _timeSlotsSubscription = null;
        print('📱 Real-time time slots subscription stopped');
      }

      _isSubscriptionActive = false;
    } catch (e) {
      print('❌ Failed to stop real-time subscriptions: $e');
    }
  }

  // Handle real-time assigned category updates (insert/update)
  static void _handleRealtimeAssignedCategoryUpdate(
      PostgresChangePayload payload) {
    try {
      final contactPhone =
          payload.newRecord['categorized_contact_phone'] as String?;
      final userId = payload.newRecord['user_id'] as String?;

      if (contactPhone == null || userId == null) return;

      print(
          '📱 Processing real-time assigned category update for phone: $contactPhone');

      // Clear the cache for this contact to force a fresh fetch
      clearCacheForContact(userId, contactPhone);

      // Proactively fetch fresh data to update cache immediately
      _refreshSpecificCacheEntry(userId, contactPhone);

      // Emit update to trigger UI refresh
      _emitAssignedCategoryUpdate(contactPhone, null);

      print('✅ Real-time assigned category update processed for $contactPhone');
    } catch (e) {
      print('❌ Failed to handle real-time assigned category update: $e');
    }
  }

  // Handle real-time assigned category deletions
  static void _handleRealtimeAssignedCategoryDelete(
      PostgresChangePayload payload) {
    try {
      final contactPhone =
          payload.oldRecord['categorized_contact_phone'] as String?;
      final userId = payload.oldRecord['user_id'] as String?;

      if (contactPhone == null || userId == null) return;

      print(
          '📱 Processing real-time assigned category deletion for phone: $contactPhone');

      // Clear the cache for this contact
      clearCacheForContact(userId, contactPhone);

      // Emit update to trigger UI refresh
      _emitAssignedCategoryUpdate(contactPhone, null);

      print(
          '✅ Real-time assigned category deletion processed for $contactPhone');
    } catch (e) {
      print('❌ Failed to handle real-time assigned category deletion: $e');
    }
  }

  // Handle real-time time slot changes (insert/update/delete)
  static void _handleRealtimeTimeSlotChange(PostgresChangePayload payload) {
    try {
      // Get the category ID from the time slot change
      String? categoryId;

      // For insert/update events, use newRecord
      if (payload.eventType == PostgresChangeEvent.insert ||
          payload.eventType == PostgresChangeEvent.update) {
        categoryId = payload.newRecord['category_id'] as String?;
      }
      // For delete events, use oldRecord
      else if (payload.eventType == PostgresChangeEvent.delete) {
        categoryId = payload.oldRecord['category_id'] as String?;
      }

      if (categoryId == null) return;

      print(
          '📱 Processing real-time time slot change for category: $categoryId');

      // Find all cached assigned categories that use this category
      final affectedCacheKeys = <String>[];

      for (final entry in _assignedCategoriesCache.entries) {
        final cachedCategory = entry.value.category;
        if (cachedCategory != null && cachedCategory.id == categoryId) {
          affectedCacheKeys.add(entry.key);
        }
      }

      print(
          '📱 Found ${affectedCacheKeys.length} cached entries affected by time slot change');

      // Clear cache for all affected entries to force fresh fetch with updated time slots
      for (final cacheKey in affectedCacheKeys) {
        _assignedCategoriesCache.remove(cacheKey);
        print('📱 Cleared cache entry: $cacheKey');
      }

      // If we have affected entries, refresh them in the background
      if (affectedCacheKeys.isNotEmpty) {
        _refreshAffectedCacheEntries(categoryId);

        // Also clear CategoryAssignmentService cache for affected contacts
        // This ensures phone dialogs get fresh data with updated time slots
        _clearCategoryAssignmentCacheForCategory(categoryId);
      }

      print('✅ Real-time time slot change processed for category $categoryId');
    } catch (e) {
      print('❌ Failed to handle real-time time slot change: $e');
    }
  }

  // Clear CategoryAssignmentService cache for contacts using a specific category
  static void _clearCategoryAssignmentCacheForCategory(String categoryId) {
    // Run in background without blocking
    Future(() async {
      try {
        final connectivityService = ConnectivityService();
        if (!connectivityService.isOnline) return;

        print(
            '🗑️ Clearing CategoryAssignmentService cache for category $categoryId');

        // Get all user_contacts that use this category
        final response = await SupabaseService.client
            .from('user_contacts')
            .select('user_id, categorized_contact_phone')
            .eq('assigned_category_id', categoryId);

        print(
            '📱 Found ${response.length} contacts using category $categoryId for cache clearing');

        // Clear CategoryAssignmentService cache for each affected contact
        for (final record in response) {
          final userId = record['user_id'] as String;
          final contactPhone = record['categorized_contact_phone'] as String;

          try {
            // Import and use CategoryAssignmentService to clear its cache
            // We need to clear cache where this contact (userId) has assigned
            // a category to someone calling from contactPhone
            CategoryAssignmentService.clearCacheForContact(
                userId, contactPhone);

            print(
                '🗑️ Cleared CategoryAssignmentService cache for contact $userId, phone $contactPhone');
          } catch (e) {
            print(
                '❌ Failed to clear CategoryAssignmentService cache for contact $userId: $e');
          }
        }

        print(
            '✅ Completed clearing CategoryAssignmentService cache for category $categoryId');
      } catch (e) {
        print('❌ Failed to clear CategoryAssignmentService cache: $e');
      }
    });
  }

  // Refresh affected cache entries when time slots change
  static void _refreshAffectedCacheEntries(String categoryId) {
    // Run in background without blocking
    Future(() async {
      try {
        final connectivityService = ConnectivityService();
        if (!connectivityService.isOnline) return;

        print(
            '🔄 Refreshing cache entries affected by time slot changes for category $categoryId');

        // Get all user_contacts that use this category
        final response = await SupabaseService.client
            .from('user_contacts')
            .select('user_id, categorized_contact_phone')
            .eq('assigned_category_id', categoryId);

        print(
            '📱 Found ${response.length} contacts using category $categoryId');

        // Refresh cache for each affected contact
        for (final record in response) {
          final userId = record['user_id'] as String;
          final contactPhone = record['categorized_contact_phone'] as String;

          try {
            // Fetch fresh data from server with updated time slots
            final freshCategory = await getAssignedCategory(
              currentUserId: userId,
              contactPhone: contactPhone,
            );

            // Emit update to trigger UI refresh for this contact
            _emitAssignedCategoryUpdate(contactPhone, freshCategory);

            print(
                '✅ Refreshed cache for contact $contactPhone with updated time slots');
          } catch (e) {
            print('❌ Failed to refresh cache for contact $contactPhone: $e');
          }
        }

        print(
            '✅ Completed refreshing all affected cache entries for category $categoryId');
      } catch (e) {
        print('❌ Failed to refresh affected cache entries: $e');
      }
    });
  }

  // Refresh a specific cache entry in the background
  static void _refreshSpecificCacheEntry(
      String currentUserId, String contactPhone) {
    // Run in background without blocking
    Future(() async {
      try {
        final connectivityService = ConnectivityService();
        if (!connectivityService.isOnline) return;

        print(
            '🔄 Refreshing specific assigned category cache entry for contact $contactPhone');

        // Fetch fresh data (this will cache it again)
        final freshCategory = await getAssignedCategory(
          currentUserId: currentUserId,
          contactPhone: contactPhone,
        );

        if (freshCategory != null) {
          print(
              '✅ Refreshed specific assigned category cache entry: ${freshCategory.type.name}');
        } else {
          print('📱 No assigned category found for specific cache refresh');
        }
      } catch (e) {
        print(
            '⚠️ Failed to refresh specific assigned category cache entry: $e');
      }
    });
  }

  // Start connection monitoring to detect and fix disconnections
  static void _startConnectionMonitoring() {
    _connectionMonitorTimer?.cancel();
    _connectionMonitorTimer =
        Timer.periodic(const Duration(minutes: 2), (timer) {
      _checkConnectionHealth();
    });
    print('📱 Started assigned categories connection monitoring');
  }

  // Check connection health and reconnect if needed
  static Future<void> _checkConnectionHealth() async {
    try {
      final connectivityService = ConnectivityService();
      if (!connectivityService.isOnline) {
        return; // Skip check if offline
      }

      // Test the connection with a simple query
      bool connectionWorking = await _testConnection();

      if (!connectionWorking) {
        print('⚠️ Assigned categories connection test failed');
        print('🔄 Attempting to reconnect assigned categories subscription...');

        // Force reconnection
        await stopRealtimeSubscription();
        await Future.delayed(const Duration(seconds: 2)); // Brief delay
        await startRealtimeSubscription();

        print('✅ Assigned categories subscription reconnection attempted');
        return;
      }

      // Check if subscription is supposed to be active but hasn't received updates recently
      if (_isSubscriptionActive && _lastUpdateReceived != null) {
        final timeSinceLastUpdate =
            DateTime.now().difference(_lastUpdateReceived!);

        // If no updates for 15 minutes and we're online, the connection might be stale
        if (timeSinceLastUpdate.inMinutes > 15) {
          print(
              '⚠️ Assigned categories subscription appears stale (${timeSinceLastUpdate.inMinutes} min since last update)');
          print(
              '🔄 Attempting to reconnect assigned categories subscription...');

          // Force reconnection
          await stopRealtimeSubscription();
          await Future.delayed(const Duration(seconds: 2)); // Brief delay
          await startRealtimeSubscription();

          print('✅ Assigned categories subscription reconnection attempted');
        }
      }
    } catch (e) {
      print('❌ Error during assigned categories connection health check: $e');
    }
  }

  // Test connection with a simple query
  static Future<bool> _testConnection() async {
    try {
      // Simple query to test if Supabase connection is working
      await SupabaseService.client
          .from('user_contacts')
          .select('id')
          .limit(1)
          .timeout(const Duration(seconds: 5));

      print('📱 Assigned categories connection test: OK');
      return true;
    } catch (e) {
      print('❌ Assigned categories connection test failed: $e');
      return false;
    }
  }

  // Handle connectivity changes
  static Future<void> onConnectivityChanged(bool isOnline) async {
    if (isOnline) {
      print(
          '📱 Device came online - starting real-time assigned categories subscription');
      await startRealtimeSubscription();
      // Refresh cache when connectivity is restored to get latest updates
      await refreshCacheOnConnectivity();
    } else {
      print(
          '📱 Device went offline - stopping real-time assigned categories subscription');
      await stopRealtimeSubscription();
    }
  }

  // Refresh assigned categories cache when connectivity is restored
  static Future<void> refreshCacheOnConnectivity() async {
    try {
      final connectivityService = ConnectivityService();
      if (!connectivityService.isOnline) {
        print('📱 Still offline - cannot refresh assigned categories cache');
        return;
      }

      print(
          '🔄 Refreshing assigned categories cache after connectivity restored...');

      // Get all cached contact-phone combinations
      final cacheKeys = _assignedCategoriesCache.keys.toList();
      int refreshedCount = 0;

      // Refresh each cached assignment
      for (final cacheKey in cacheKeys) {
        try {
          // Parse cache key to extract currentUserId and contactPhone
          final parts = cacheKey.split('_');
          if (parts.length >= 2) {
            final currentUserId = parts[0];
            final contactPhone = parts.sublist(1).join('_');

            print(
                '🔄 Refreshing assigned category cache for user $currentUserId, contact $contactPhone');

            // Remove from cache to force fresh fetch
            _assignedCategoriesCache.remove(cacheKey);

            // Fetch fresh data (this will cache it again)
            final freshCategory = await getAssignedCategory(
              currentUserId: currentUserId,
              contactPhone: contactPhone,
            );

            if (freshCategory != null) {
              print(
                  '✅ Refreshed assigned category for $contactPhone: ${freshCategory.type.name}');
              refreshedCount++;
            } else {
              print(
                  '📱 No assigned category found for $contactPhone after refresh');
            }
          }
        } catch (e) {
          print(
              '⚠️ Failed to refresh assigned category cache for key $cacheKey: $e');
          // Continue with other cache entries even if one fails
        }

        // Small delay to avoid overwhelming the server
        await Future.delayed(const Duration(milliseconds: 100));
      }

      // Save refreshed cache to persistent storage
      await _savePersistentAssignedCategoriesCache();

      print(
          '✅ Assigned categories cache refresh completed: $refreshedCount entries refreshed');
    } catch (e) {
      print('❌ Failed to refresh assigned categories cache: $e');
    }
  }

  // Force refresh subscription (useful for debugging)
  static Future<void> forceRefreshSubscription() async {
    print('🔄 Force refreshing assigned categories subscription...');
    await stopRealtimeSubscription();
    await Future.delayed(const Duration(seconds: 1));
    await startRealtimeSubscription();
    print('✅ Assigned categories subscription force refresh completed');
  }

  static void dispose() {
    _assignedCategoriesUpdateController.close();
    _cacheCleanupTimer?.cancel();
    _connectionMonitorTimer?.cancel();
    stopRealtimeSubscription();
  }
}
