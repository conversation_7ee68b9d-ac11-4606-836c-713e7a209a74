import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';
import '../models/models.dart';
import '../services/contacts_service.dart';

class CallingService {
  static Future<void> makeCall({
    required Profile contactProfile,
    required Category? contactCategory,
    required String callerName,
    String?
        specificPhoneNumber, // Optional: use this phone number instead of profile's phone number
  }) async {
    // IMPORTANT: Emergency status check should be done BEFORE calling this method
    // This method assumes emergency checks have already been performed
    // The calling screen/widget should handle emergency dialogs before calling this method

    // Launch phone dialer directly without showing notification alerts
    final phoneToCall = specificPhoneNumber ?? contactProfile.phoneNumber;
    await _launchPhoneDialer(phoneToCall);

    // Note: Removed notification alerts for production app
    // Users expect direct dialing behavior when clicking phone numbers
  }

  static Future<void> _launchPhoneDialer(String phoneNumber) async {
    final cleanedNumber = ContactsService.cleanPhoneNumber(phoneNumber);
    final uri = Uri.parse('tel:$cleanedNumber');

    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      throw Exception('Could not launch phone dialer');
    }
  }

  /// Launch phone dialer with the given phone number (public method)
  static Future<void> launchDialer(String phoneNumber) async {
    await _launchPhoneDialer(phoneNumber);
  }

  /// Send SMS with the given message to the phone number
  static Future<void> sendSMS(String phoneNumber, String message) async {
    try {
      final cleanedNumber = ContactsService.cleanPhoneNumber(phoneNumber);
      print('📱 Attempting to send SMS to: $cleanedNumber');

      // Try different SMS URI formats for better compatibility
      final uriFormats = [
        'sms:$cleanedNumber?body=${Uri.encodeComponent(message)}',
        'sms:$cleanedNumber&body=${Uri.encodeComponent(message)}',
        'sms:$cleanedNumber',
      ];

      bool launched = false;
      for (final uriString in uriFormats) {
        try {
          final uri = Uri.parse(uriString);
          print('📱 Trying SMS URI: $uriString');

          if (await canLaunchUrl(uri)) {
            await launchUrl(uri, mode: LaunchMode.externalApplication);
            launched = true;
            print('✅ SMS app launched successfully');
            break;
          }
        } catch (e) {
          print('❌ Failed to launch SMS with URI $uriString: $e');
          continue;
        }
      }

      if (!launched) {
        throw Exception(
            'Could not launch SMS app. Please check if SMS app is available on your device.');
      }
    } catch (e) {
      print('❌ SMS sending failed: $e');
      rethrow;
    }
  }

  static bool _isGoodTimeToCall(Category category) {
    if (category.type == CategoryType.contactAnytime) {
      return true;
    }

    if (category.timeSlots.isEmpty) {
      return category.type == CategoryType.preferAnytime;
    }

    // Check if current time falls within any of the time slots
    return category.timeSlots.any((slot) => slot.isCurrentTimeInSlot());
  }

  static String getCallingSuggestion(Category? category, BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    if (category == null) {
      return l10n.callingSuggestionNoCategory;
    }

    switch (category.type) {
      case CategoryType.contactAnytime:
        return l10n.callingSuggestionContactAnytime;

      case CategoryType.preferAnytime:
        if (category.timeSlots.isEmpty) {
          return l10n.callingSuggestionContactAnytime;
        }
        final isGoodTime = _isGoodTimeToCall(category);
        if (isGoodTime) {
          return l10n.callingSuggestionPreferAnytimeGoodTime;
        } else {
          return l10n.callingSuggestionPreferAnytimeBadTime;
        }

      case CategoryType.contactAtTimes:
        final isGoodTime = _isGoodTimeToCall(category);
        if (isGoodTime) {
          return l10n.callingSuggestionContactAtTimesGoodTime;
        } else {
          return l10n.callingSuggestionContactAtTimesBadTime;
        }

      case CategoryType.contactThroughMessages:
        return l10n.callingSuggestionContactThroughMessages;
    }
  }

  static List<TimeSlot> getNextAvailableSlots(Category category) {
    if (category.timeSlots.isEmpty) return [];

    final now = DateTime.now();
    final currentDay = now.weekday % 7;
    final currentTime = CustomTimeOfDay(hour: now.hour, minute: now.minute);

    // Find slots for today that are still upcoming
    final todaySlots = category.timeSlots
        .where((slot) =>
            slot.dayOfWeek == currentDay &&
            currentTime.isBeforeOrEqual(slot.startTime))
        .toList();

    if (todaySlots.isNotEmpty) {
      return todaySlots;
    }

    // Find slots for the next few days
    final upcomingSlots = <TimeSlot>[];
    for (int i = 1; i <= 7; i++) {
      final targetDay = (currentDay + i) % 7;
      final daySlots = category.timeSlots
          .where((slot) => slot.dayOfWeek == targetDay)
          .toList();

      if (daySlots.isNotEmpty) {
        upcomingSlots.addAll(daySlots);
        if (upcomingSlots.length >= 3) break; // Limit to next 3 slots
      }
    }

    return upcomingSlots.take(3).toList();
  }
}
